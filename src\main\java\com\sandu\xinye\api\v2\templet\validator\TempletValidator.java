package com.sandu.xinye.api.v2.templet.validator;

import com.jfinal.core.Controller;
import com.sandu.xinye.common.validator.BaseValidator;

public class TempletValidator extends BaseValidator {

	@Override
	protected void validate(Controller c) {
		validateParaNotNull("name", "模板名称");
		validateParaNotNull("cover", "图片");
		validateParaNotNull("data", "标签内容数据");
		validateParaNotNull("gap", "间隙");
		validateParaNotNull("height", "高度");
		validateParaNotNull("width", "宽度");
		validateParaNotNull("printDirection", "打印方向");
		validateParaNotNull("paperType", "纸张类型");
		
		validateInteger("height", "msg", "高度必须传整数");
		validateInteger("width", "msg", "宽度必须传整数");
		validateInteger("printDirection", "msg", "打印必须传整数");
		validateInteger("paperType", "msg", "纸张类型必须传整数");
		validateLength("name", 32, "msg", "分组名称长度不超过32个字符");
	}

	@Override
	protected void handleError(Controller c) {
		renderErroJson(c);
	}

}
