package com.sandu.xinye.common.kit;

import com.aliyun.dypnsapi20170525.models.GetMobileRequest;
import com.aliyun.dypnsapi20170525.models.GetMobileResponse;
import com.aliyun.dysmsapi20170525.models.SendMessageToGlobeRequest;
import com.aliyun.dysmsapi20170525.models.SendMessageToGlobeResponse;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.teaopenapi.models.Config;
import com.jfinal.kit.PropKit;
import org.apache.log4j.Logger;

/**
 * 阿里云openApi
 *
 * <AUTHOR>
 * @date 2021/10/11
 */
public class AliOpenapiKit {

	private static final Logger logger = Logger.getLogger(AliOpenapiKit.class);
	private static final com.jfinal.kit.Prop prop = PropKit.use("aliopenapi_config.txt");

	/**
	 * AK&SK
	 */
	private final String ACCESS_KEY_ID = prop.get("sms.accessKeyId");
	private final String ACCESS_KEY_SECRET = prop.get("sms.accessKeySecret");

	/**
	 * 国际短信单独通道 SendMessageToGlobe 模板
	 */
	private final String GLOBE_MESSAGE_TEMPLET_FORMAT = "【XPrinter】您的驗證碼為：%s，請勿泄露於他人！";

	/**
	 * 使用AK&SK初始化账号Client
	 *
	 * @param accessKeyId
	 * @param accessKeySecret
	 * @return Client
	 * @throws Exception
	 */
	public static com.aliyun.dypnsapi20170525.Client createDypnsClient(String accessKeyId, String accessKeySecret) throws Exception {
		Config config = new Config()
				// 您的AccessKey ID
				.setAccessKeyId(accessKeyId)
				// 您的AccessKey Secret
				.setAccessKeySecret(accessKeySecret);
		// 访问的域名
		config.endpoint = "dypnsapi.aliyuncs.com";
		return new com.aliyun.dypnsapi20170525.Client(config);
	}

	public static com.aliyun.dysmsapi20170525.Client createDysmsClient(String accessKeyId, String accessKeySecret) throws Exception {
		Config config = new Config()
				// 您的AccessKey ID
				.setAccessKeyId(accessKeyId)
				// 您的AccessKey Secret
				.setAccessKeySecret(accessKeySecret);
		// 访问的域名
		config.endpoint = "dysmsapi.aliyuncs.com";
		return new com.aliyun.dysmsapi20170525.Client(config);
	}

	/**
	 * 一键登录取号
	 *
	 * @param accessToken
	 * @return
	 */
	public String GetMobile(String accessToken) {
		String phone = "";
		logger.info("一键登录获取手机号开始---------------- token:\r\n" + accessToken);
		try {
			com.aliyun.dypnsapi20170525.Client client = AliOpenapiKit.createDypnsClient(ACCESS_KEY_ID, ACCESS_KEY_SECRET);
			GetMobileRequest getMobileRequest = new GetMobileRequest()
					.setAccessToken(accessToken)
					.setOutId("xprinter2.0");
			GetMobileResponse resp = client.getMobile(getMobileRequest);

			if (resp.body.code.equals("OK")) {
				phone = resp.body.getMobileResultDTO.mobile;
				logger.info("一键登录获取手机号成功！  phone: " + phone);
			} else {
				logger.info("一键登录获取手机号失败！错误代码：" + resp.body.code);
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage());
		}

		logger.info("一键登录获取手机号结束---------------- phone " + phone);
		return phone;
	}

	/**
	 * 发送短信验证码
	 *
	 * @param phone
	 * @param code
	 * @return
	 */
	public String SendSMS(String phone, String code) {
		String result = "error";
		logger.info("发送短信开始----------------,code:" + code + ";" + String.format("{code: \"%s\"}", code));
		try {
			com.aliyun.dysmsapi20170525.Client client = AliOpenapiKit.createDysmsClient(ACCESS_KEY_ID, ACCESS_KEY_SECRET);
			logger.info("client.sendSms before: signName：" + prop.get("sms.signName") + ";templateCode:" + prop.get("sms.templateCode"));
			SendSmsRequest sendSmsRequest = new SendSmsRequest()
					.setPhoneNumbers(phone)
					.setSignName(prop.get("sms.signName"))
					.setTemplateCode(prop.get("sms.templateCode"))
					.setTemplateParam(String.format("{code:%s}", code));

			logger.info("client.sendSms SendSmsRequest");
			SendSmsResponse resp = client.sendSms(sendSmsRequest);
			logger.info("client.sendSms after:" + resp.body.code);

			result = resp.body.code;
			if (resp.body.code.equals("OK")) {
				logger.info(String.format("短信发送成功！ phone: %s  code: %s", phone, code));
			} else {
				logger.info("发送短信验证码！错误代码：" + resp.body.code);
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("发送验证码错误！");
			logger.error(e.getMessage());
		}

		logger.info("发送短信结束----------------");
		return result;
	}

	/**
	 * 发送国际短信验证码
	 *
	 * @param phone
	 * @param code
	 * @return
	 */
	public String SendInternationalSMS(String phone, String code) {
		String result = "error";
		logger.info("发送短信开始----------------,code:" + code + ";" + String.format("{code: \"%s\"}", code));
		try {
			String signName = prop.get("sms.intern.signName");
			String templateCode = prop.get("sms.intern.templateCode");
			com.aliyun.dysmsapi20170525.Client client = AliOpenapiKit.createDysmsClient(ACCESS_KEY_ID, ACCESS_KEY_SECRET);
			logger.info("client.sendInternationalSms before: signName：" + signName + ";templateCode:" + templateCode);
			SendSmsRequest sendSmsRequest = new SendSmsRequest()
					.setPhoneNumbers(phone)
					.setSignName(signName)
					.setTemplateCode(templateCode)
					.setTemplateParam(String.format("{code:%s}", code));

			logger.info("client.sendInternationalSms SendSmsRequest");
			SendSmsResponse resp = client.sendSms(sendSmsRequest);
			logger.info("client.sendInternationalSms after:" + resp.body.code);

			result = resp.body.code;
			if (result.equals("OK")) {
				logger.info(String.format("短信发送成功！ phone: %s  code: %s", phone, code));
			} else {
				logger.info("发送短信验证码！错误代码：" + resp.body.code);
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("发送验证码错误！");
			logger.error(e.getMessage());
		}

		logger.info("发送短信结束----------------");
		return result;
	}

	/**
	 * 台湾地区政策收紧
	 * 需要单独通道
	 *
	 * @param phone
	 * @param code
	 * @return
	 */
	public String SendToTaiwanSms(String phone, String code) {
		String result = "error";
		logger.info("发送短信开始----------------,code:" + code + ";" + String.format("{code: \"%s\"}", code));
		String globeMessage = String.format(GLOBE_MESSAGE_TEMPLET_FORMAT, code);
		try {
			com.aliyun.dysmsapi20170525.Client client = AliOpenapiKit.createDysmsClient(ACCESS_KEY_ID, ACCESS_KEY_SECRET);
			logger.info("client.SendToTaiwanSms SendSmsRequest");

			SendMessageToGlobeRequest globeRequest = new SendMessageToGlobeRequest()
					.setFrom("Alirich")
					.setType("OTP")
					.setTo(phone)
					.setMessage(globeMessage);

			SendMessageToGlobeResponse resp = client.sendMessageToGlobe(globeRequest);
			logger.info("client.SendToTaiwanSms after:" + resp.body.code);

			result = resp.body.code;
			if (result.equals("OK")) {
				logger.info(String.format("台湾短信发送成功！ phone: %s  code: %s", phone, code));
			} else {
				logger.info("台湾发送短信验证码！错误代码：" + resp.body.code);
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("台湾发送验证码错误！");
			logger.error(e.getMessage());
		}

		logger.info("发送短信结束----------------");
		return result;
	}
}
