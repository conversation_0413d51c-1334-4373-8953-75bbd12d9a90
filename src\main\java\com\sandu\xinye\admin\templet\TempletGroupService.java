package com.sandu.xinye.admin.templet;

import com.jfinal.kit.Kv;
import com.jfinal.kit.PathKit;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.IAtom;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.SqlPara;
import com.sandu.xinye.admin.operate.OperationLogService;
import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.kit.UploadFileMoveKit;
import com.sandu.xinye.common.model.Font;
import com.sandu.xinye.common.model.Machine;
import com.sandu.xinye.common.model.SysUser;
import com.sandu.xinye.common.model.TempletGroup;

import java.io.File;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;

public class TempletGroupService {
	public static final TempletGroupService me = new TempletGroupService();

	public RetKit list(int pageSize, int pageNumber, Kv kv) {
		String groupName = kv.getStr("groupName");
		if (StrKit.notBlank(groupName)) {
			groupName = "%" + groupName + "%";
			kv.set("groupName", groupName);
		}
		SqlPara sqlPara = Db.getSqlPara("admin.templet.group.paginate", kv);
		Page<Font> page = Font.dao.paginate(pageNumber, pageSize, sqlPara);
		return RetKit.ok("page", page);
	}

	public RetKit add(TempletGroup tgroup, SysUser sysUser, String ip) {
		boolean succ = tgroup.setType(Constant.TEMPLET_GROUP_TYPE_BUSINESS).setCreateTime(new Date()).setUserId(sysUser.getSysUserId()).save();
		if (succ) {
			String content = sysUser.getSysUserName() + "添加了id为" + tgroup.getId() + "的" + tgroup.getName() + "模板组";
			OperationLogService.me.saveOperationLog(sysUser.getSysUserId(), ip, content);
		}
		return succ ? RetKit.ok() : RetKit.fail();
	}

	public RetKit update(TempletGroup tgroup, SysUser sysUser, String ip) {
		boolean succ = tgroup.setUserId(sysUser.getSysUserId()).update();
		if(succ){
			String content = sysUser.getSysUserName() + "编辑了id为" + tgroup.getId()  +"的" + tgroup.getName() + "模板组";
			OperationLogService.me.saveOperationLog(sysUser.getSysUserId(), ip, content);
		}
		return succ ? RetKit.ok().setOk("编辑成功") : RetKit.fail();
	}

	public RetKit del(String id, SysUser sysUser,String ip){
		TempletGroup tgroup = TempletGroup.dao.findById(id);
		boolean succ = tgroup.delete();
		if(succ){
			String content = sysUser.getSysUserName() + "删除了id为" + id +"的" + tgroup.getName() +"设备";
			OperationLogService.me.saveOperationLog(sysUser.getSysUserId(), ip, content);
		}
		return succ ? RetKit.ok().setOk("删除成功") : RetKit.fail();
	}

	public RetKit getGroupList(String userId){
		String sql = String.format("select * from templet_group where type = 1 order by id desc");
		if(StrKit.notBlank(userId)){
			sql = String.format("select * from templet_group where userId = %s order by id desc", userId);
		}

		List<Machine> list = Machine.dao.find(sql);
		return RetKit.ok("list",list);
	}

}
