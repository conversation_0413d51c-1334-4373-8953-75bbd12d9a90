
package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by <PERSON><PERSON><PERSON>, do not modify this file.
 */
@SuppressWarnings({ "serial", "unchecked" })
public abstract class BaseArchiveRecords<M extends BaseArchiveRecords<M>> extends Model<M> implements IBean {

  public M setId(java.lang.Integer id) {
    set("id", id);
    return (M) this;
  }

  public java.lang.Integer getId() {
    return getInt("id");
  }

  public M setOriginalTableName(java.lang.String originalTableName) {
    set("originalTableName", originalTableName);
    return (M) this;
  }

  public java.lang.String getOriginalTableName() {
    return getStr("originalTableName");
  }

  public M setOriginalRecordId(java.lang.String originalRecordId) {
    set("originalRecordId", originalRecordId);
    return (M) this;
  }

  public java.lang.String getOriginalRecordId() {
    return getStr("originalRecordId");
  }

  public M setRecordContent(java.lang.String recordContent) {
    set("recordContent", recordContent);
    return (M) this;
  }

  public java.lang.String getRecordContent() {
    return getStr("recordContent");
  }

  public M setOriginalDeleteTime(java.util.Date originalDeleteTime) {
    set("originalDeleteTime", originalDeleteTime);
    return (M) this;
  }

  public java.util.Date getOriginalDeleteTime() {
    return get("originalDeleteTime");
  }

  public M setArchiveTime(java.util.Date archiveTime) {
    set("archiveTime", archiveTime);
    return (M) this;
  }

  public java.util.Date getArchiveTime() {
    return get("archiveTime");
  }

}