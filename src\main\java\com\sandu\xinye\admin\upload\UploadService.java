package com.sandu.xinye.admin.upload;

import com.jfinal.kit.Kv;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.PropKit;
import com.jfinal.kit.StrKit;
import com.jfinal.upload.UploadFile;
import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.kit.*;
import com.xiaoleilu.hutool.io.FileUtil;
import com.xiaoleilu.hutool.util.CollectionUtil;
import com.xiaoleilu.hutool.util.StrUtil;

import java.io.File;
import java.util.List;

public class UploadService {
    public static final UploadService me = new UploadService();

    private static final String UPLOAD_PATH = PropKit.get("uploadPath") + "/temp/";
    private static final String ossCdnDomain = "http://img.ycjqb.com";
    private static final String ossImgBucketName = PropKit.get("imgBucketName");
    private static final String UPLOAD_LOGO_OSS_PREFIX = "img/logo/";
    private static final String UPLOAD_FONT_OSS_PREFIX = "font/";
    private static final String UPLOAD_FONT_COVER_OSS_PREFIX = "font/cover/";

    /**
     * 上传图片
     *
     * @param uf
     * @return
     */

    public RetKit uploadImg(UploadFile uf) {
        LogKit.info("uploadImg=== 上传图片");
        if (uf == null) {
            LogKit.error("=== 上传图片不能为空");
            return RetKit.fail("上传图片不能为空");
        }
        try {
            if (!ImageKit.isImageExtName(uf.getFileName())) {
                LogKit.error("uploadImg=== 文件类型不正确，只支持类型：jpg、jpeg、png、bmp");
                return RetKit.fail("msg", "文件类型不正确，只支持类型：jpg、jpeg、png、bmp");
            }
            // 获得文件的后缀名
            String extName = MyFileKit.getExtName(uf.getFileName());
            String newFileName = StrKit.getRandomUUID() + "." + extName;
            String newPath = Constant.BASE_UPLOAD_PATH + "/" + newFileName; // 服务器中的文件新路径

            File newFile = new File(newPath);
            if (newFile.exists()) {
                boolean res = newFile.delete();
                if (!res) {
                    LogKit.error("uploadImg=== 转存图片失败");
                    return RetKit.fail("转存图片失败!");
                }
            }

            boolean succ = uf.getFile().renameTo(newFile);

            succ = moveFile(newFile, newFileName);

            if (!succ) {
                LogKit.error("uploadImg=== 失败");
            } else {
                LogKit.info("uploadImg=== 上传文件成功");
            }

            return succ ? RetKit.ok("file", Kv.by("url", Constant.UPLOAD_PATH + "/" + newFileName))
                    : RetKit.fail("msg", "上传文件失败！");
        } catch (Exception e) {
            LogKit.error("上传文件失败：" + e.getMessage());
            return RetKit.fail("msg", e.getMessage());
        } finally {
            LogKit.info("清除临时文件" + uf.getFile().getName());
            uf.getFile().delete();
        }
    }

    /**
     * 上传logo图片
     *
     * @param uf
     * @return
     */
    public RetKit uploadLogo(UploadFile uf) {
        if (uf == null) {
            return RetKit.fail("上传logo图片能为空");
        }
        try {
            if (!ImageKit.isImageExtName(uf.getFileName())) {
                LogKit.error("uploadImg=== 文件类型不正确，只支持类型：jpg、jpeg、png、bmp");
                return RetKit.fail("msg", "文件类型不正确，只支持类型：jpg、jpeg、png、bmp");
            }
            // 获得文件的后缀名
            String extName = MyFileKit.getExtName(uf.getFileName());
            String newFileName = StrKit.getRandomUUID() + "." + extName;
            // 上传图片到OSS
            String objectName = UPLOAD_LOGO_OSS_PREFIX + newFileName;
            boolean succ = AliOssKit.createObject(ossImgBucketName, objectName, uf.getFile());
            if (!succ) {
                LogKit.error("uploadLogo=== 失败");
            } else {
                LogKit.info("uploadLogo=== 上传文件成功");
            }

            return succ ? RetKit.ok("file", Kv.by("url", ossCdnDomain + "/" + objectName))
                    : RetKit.fail("msg", "上传文件失败！");
        } catch (Exception ex) {
            return RetKit.fail(ex.getMessage());
        }
    }


    /**
     * 上传字体
     *
     * @param uf
     * @return
     */
    public RetKit uploadFont(UploadFile uf, String prefix) {
        if (uf == null) {
            return RetKit.fail("上传字体不能为空");
        }
        try {
            // 获得文件的后缀名
            String extName = MyFileKit.getExtName(uf.getFileName());
            String newFileName = StrUtil.isNotEmpty(prefix) ? prefix + "/" + uf.getFileName() : StrKit.getRandomUUID() + "." + extName;
            LogKit.debug("upload font, newFileName: " + newFileName);
            // 上传图片到OSS
            String objectName = UPLOAD_FONT_OSS_PREFIX + newFileName;
            boolean succ = AliOssKit.createObject(ossImgBucketName, objectName, uf.getFile());

            if (!succ) {
                LogKit.error("uploadLogo=== 失败");
            } else {
                LogKit.info("uploadLogo=== 上传文件成功");
            }

            return succ ? RetKit.ok("file", Kv.by("url", ossCdnDomain + "/" + objectName))
                    : RetKit.fail("msg", "上传文件失败！");
        } catch (Exception ex) {
            return RetKit.fail(ex.getMessage());
        }
    }

    /**
     * 上传字体封面图片
     *
     * @param uf
     * @return
     */
    public RetKit uploadFontCover(UploadFile uf, String prefix) {
        if (uf == null) {
            return RetKit.fail("上传字体封面不能为空");
        }
        try {
            // 获得文件的后缀名
            String extName = MyFileKit.getExtName(uf.getFileName());
            String newFileName = StrUtil.isNotEmpty(prefix) ? prefix + "/" + uf.getFileName() : StrKit.getRandomUUID() + "." + extName;
            LogKit.debug("upload font, newFileName: " + newFileName);
            // 上传图片到OSS
            String objectName = UPLOAD_FONT_COVER_OSS_PREFIX + newFileName;
            boolean succ = AliOssKit.createObject(ossImgBucketName, objectName, uf.getFile());

            if (!succ) {
                LogKit.error("uploadLogo=== 失败");
            } else {
                LogKit.info("uploadLogo=== 上传文件成功");
            }

            return succ ? RetKit.ok("file", Kv.by("url", ossCdnDomain + "/" + objectName))
                    : RetKit.fail("msg", "上传文件失败！");
        } catch (Exception ex) {
            return RetKit.fail(ex.getMessage());
        }
    }

    /**
     * @param uf
     * @return
     * @Title: uploadVideo
     * @Description: 上传视频文件
     * @date 2019年5月8日 下午5:01:29
     * <AUTHOR>
     */
    public RetKit uploadVideo(UploadFile uf) {
        if (uf == null) {
            return RetKit.fail("上传视频不能为空！");
        }
        try {
            if (!VideoKit.isVideoExtName(uf.getFileName())) {
                return RetKit.fail("文件类型不正确，只支持类型：mp3，wma，wav，ogg，mp4，flv，avi，rm，rmvb，wmv，mpg等格式！");
            }
            // 获得文件的后缀名
//			String extName = MyFileKit.getExtName(uf.getFileName());
//			String newFileName = StrKit.getRandomUUID() + "." + extName;
            String newFileName = uf.getFileName();
            String newPath = Constant.BASE_UPLOAD_PATH + "/" + newFileName;

            File newFile = new File(newPath);
//			if (newFile.exists()) {
//				boolean res = newFile.delete();
//				if (!res) {
//					return RetKit.fail("转存视频失败！");
//				}
//			}
//			boolean succ = uf.getFile().renameTo(newFile);

            boolean succ = moveFile(newFile, newFileName);

            return succ ? RetKit.ok("file", Kv.by("url", Constant.UPLOAD_PATH + "/" + newFileName))
                    : RetKit.fail("msg", "上传文件失败！");
        } catch (Exception e) {
            LogKit.info(e.getMessage());
            return RetKit.fail("msg", e.getMessage());
        } finally {
            uf.getFile().delete();
        }
    }

    /**
     * @param uf
     * @return
     * @Title: uploadFont
     * @Description:
     * @date 2019年5月8日 下午5:01:40
     * <AUTHOR>
     */
    public RetKit uploadApk(UploadFile uf) {
        if (uf == null) {
            return RetKit.fail("上传文件不能为空");
        }
        try {
            // 获得文件的后缀名
            String extName = MyFileKit.getExtName(uf.getFileName());
            if (!(extName.toLowerCase().equals("apk"))) {
                return RetKit.fail("文件类型不正确，只支持类型：apk格式！");
            }
            String newFileName = StrKit.getRandomUUID() + "." + extName;
            String newPath = Constant.BASE_UPLOAD_PATH + "/" + newFileName;

            File newFile = new File(newPath);
            boolean succ = uf.getFile().renameTo(newFile);

            succ = moveFile(newFile, newFileName);

            return succ ? RetKit.ok("file", Kv.by("url", Constant.UPLOAD_PATH + "/" + newFileName))
                    : RetKit.fail("msg", "上传文件失败！");
        } catch (Exception e) {
            LogKit.info(e.getMessage());
            return RetKit.fail("msg", e.getMessage());
        } finally {
            uf.getFile().delete();
        }
    }

    private boolean moveFile(File file, String newFileName) {
        // 把文件复制到服务器的外部路径
        String destPath = UPLOAD_PATH + newFileName;
        boolean succ = true;
        try {
            FileUtil.move(file, new File(destPath), true);
        } catch (Exception e) {
            LogKit.error(e.getMessage());
            succ = false;
        }
        return succ;
    }
}
