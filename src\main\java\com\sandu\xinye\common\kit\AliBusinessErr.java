package com.sandu.xinye.common.kit;

/**
 * @Author: wangpf
 * @Date: 2022/1/6 16:03
 * @Description: 阿里API错误码
 */
public enum AliBusinessErr {
    ISV_BUSINESS_LIMIT_CONTROL("isv.BUSINESS_LIMIT_CONTROL", "短信发送频率超限，请稍后重试"),
    ISV_MOBILE_NUMBER_ILLEGAL("isv.MOBILE_NUMBER_ILLEGAL", "手机号码格式错误，请检查"),
    ISV_INVALID_PARAMETERS("isv.INVALID_PARAMETERS", "短信服务异常，请重试"),
    ISV_TEMPLATE_PARAMS_ILLEGAL("isv.TEMPLATE_PARAMS_ILLEGAL", "短信服务异常，请重试"),
    ISV_AMOUNT_NOT_ENOUGH("isv.AMOUNT_NOT_ENOUGH", "短信服务余额不足，请联系管理员"),
    MonthLimitControl("MonthLimitControl", "短信发送数量超过月限制，请联系管理员"),
    PhoneNumber_Illegal("PhoneNumber.Illegal", "手机号码无效或者错误，请检查");

    // 成员变量
    private String errorCode;
    private String errorInfo;
    // 构造方法
    AliBusinessErr(String errorCode, String errorInfo) {
        this.errorCode = errorCode;
        this.errorInfo = errorInfo;
    }

    // 普通方法
    public static String getErrorInfo(String errorCode) {
        for (AliBusinessErr c : AliBusinessErr.values()) {
            if (c.getErrorCode().equals(errorCode))  {
                return c.errorInfo;
            }
        }
        return null;
    }

    public static Boolean hasError(String errorCode) {
        for (AliBusinessErr c : AliBusinessErr.values()) {
            if (c.getErrorCode().equals(errorCode))  {
                return true;
            }
        }
        return false;
    }

    // get set 方法
    public String getErrorCode() {
        return errorCode;
    }
    // get set 方法
    public String getErrorInfo() {
        return errorInfo;
    }
}
