package com.sandu.xinye.api.v2.aliyun;

import com.jfinal.aop.Before;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.interceptor.GlobalInterceptor;
import com.sandu.xinye.common.kit.RetKit;

public class BarcodeController extends AppController {

  /**
   * 商品条码查询
   */
  @Before(GlobalInterceptor.class)
  public void query() {
    String code = getPara("code");
    RetKit ret = BarcodeBusiService.me.queryProductInfo(code);
    renderJson(ret);
  }

}