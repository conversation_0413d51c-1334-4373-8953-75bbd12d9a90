package com.sandu.xinye.api.v2.machine;

import com.jfinal.kit.Kv;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.SqlPara;
import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.AppDict;
import com.sandu.xinye.common.model.Help;
import com.sandu.xinye.common.model.Machine;
import com.xiaoleilu.hutool.util.CollectionUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class MachineService {

    public static final MachineService me = new MachineService();

    /**
     * @param name
     * @return
     * @Title: getDeviveList
     * @Description:
     * <AUTHOR>
     */
    public RetKit getDeviveList(String name, String locale) {
        if (StrKit.notBlank(name)) {
            name = "%" + name + "%";
        }

        if (StrKit.isBlank(locale) || locale.equals(Constant.LOCALE_ZH_CN)) {
            SqlPara sqlPara = Db.getSqlPara("app.machine.list", Kv.by("name", name));
            List<Machine> list = Machine.dao.find(sqlPara);
            return RetKit.ok("list", list);
        }

        String sql = String.format("select m.machineId, " +
                " ifnull(i18n.machineName, m.machineName) as machineName, " +
                " m.createTime " +
                " from machine m" +
                " left join machine_i18n i18n on i18n.machineId = m.machineId and i18n.locale = '%s' " +
                " where m.machineName like '%s' " +
                " order by m.createTime asc", locale, StrKit.isBlank(name) ? '%' : name);
        LogKit.error(sql);
        List<Help> list = Help.dao.find(sql);
        return RetKit.ok("list", list);
    }

    /**
     * @param
     * @return
     * @Title: hiddenType
     * @Description: APP搜索需要过滤隐藏的设备类型
     * <AUTHOR>
     */
    public RetKit hiddenTypeList() {
        // 过滤设备的字典KEY
        String hiddenKey = "bluetooth-device-hidden";

        String sql = String.format("select * from app_dict where dictKey = '%s' ", hiddenKey);
        List<AppDict> list = AppDict.dao.find(sql);
        if (CollectionUtil.isNotEmpty(list)) {
            List<String> hiddenTypeList = list.stream().map(appDict -> appDict.getDictValue()).collect(Collectors.toList());
            return RetKit.ok("list", hiddenTypeList);
        }
        return RetKit.ok("list", new ArrayList<>());
    }
}
