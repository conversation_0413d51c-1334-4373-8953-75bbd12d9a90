package com.sandu.xinye.common.kit;

import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.CopyObjectResult;
import org.apache.log4j.Logger;

import java.io.File;

/**
 * 阿里云工具类
 * OSS
 *
 * <AUTHOR>
 * @date 2022/05/11
 */
public class AliOssKit {
	private static final Logger logger = Logger.getLogger(AliOssKit.class);

	private static final String ACCESS_KEY_ID = "LTAI5tRVeUmndjxg2wXqYq7n";
	private static final String ACCESS_KEY_SECRET = "******************************";


	private static com.aliyun.oss.OSS createOssClient() {
		// 访问的域名
		String endpoint = "https://oss-cn-shenzhen.aliyuncs.com";

		// 创建OSSClient实例。
		return new OSSClientBuilder().build(endpoint, ACCESS_KEY_ID, ACCESS_KEY_SECRET);
	}

	/**
	 * @param bucketName OOS bucketName
	 * @param objectName OOS objectName
	 * @param file 文件
	 * @return true 成功， false 失败
	 * @方法描述: 上传文件到 OSS
	 */
	public static boolean createObject(String bucketName, String objectName, File file) {
		// 创建OSSClient实例。
		OSS ossClient = createOssClient();

		try {

			ossClient.putObject(bucketName, objectName, file);
		} catch (OSSException oe) {
			System.out.println("Caught an OSSException, which means your request made it to OSS, "
					+ "but was rejected with an error response for some reason.");
			System.out.println("Error Message:" + oe.getErrorMessage());
			System.out.println("Error Code:" + oe.getErrorCode());
			System.out.println("Request ID:" + oe.getRequestId());
			System.out.println("Host ID:" + oe.getHostId());

			logger.info("Error Message:" + oe.getErrorMessage());
			return false;
		} catch (ClientException ce) {
			System.out.println("Caught an ClientException, which means the client encountered "
					+ "a serious internal problem while trying to communicate with OSS, "
					+ "such as not being able to access the network.");

			logger.info("Error Message:" + ce.getMessage());
			return false;
		} finally {
			if (ossClient != null) {
				ossClient.shutdown();
			}
		}

		return true;
	}

	/**
	 * @param sourceBucketName OOS sourceBucketName
	 * @param destinationBucketName OOS destinationBucketName
	 * @return true 成功， false 失败
	 * @方法描述: 上传文件到 OSS
	 */
	public static boolean copyObject(String sourceBucketName, String sourceKey, String destinationBucketName, String destinationKey) {
		// 创建OSSClient实例。
		OSS ossClient = createOssClient();

		try {
			// 拷贝文件。
			CopyObjectResult result = ossClient.copyObject(sourceBucketName, sourceKey, destinationBucketName, destinationKey);
			System.out.println("ETag: " + result.getETag() + " LastModified: " + result.getLastModified());
		} catch (OSSException oe) {
			System.out.println("Caught an OSSException, which means your request made it to OSS, "
					+ "but was rejected with an error response for some reason.");
			System.out.println("Error Message:" + oe.getErrorMessage());
			System.out.println("Error Code:" + oe.getErrorCode());
			System.out.println("Request ID:" + oe.getRequestId());
			System.out.println("Host ID:" + oe.getHostId());
		} catch (ClientException ce) {
			System.out.println("Caught an ClientException, which means the client encountered "
					+ "a serious internal problem while trying to communicate with OSS, "
					+ "such as not being able to access the network.");
			System.out.println("Error Message:" + ce.getMessage());
		} finally {
			if (ossClient != null) {
				ossClient.shutdown();
			}
		}

		return true;
	}
}
