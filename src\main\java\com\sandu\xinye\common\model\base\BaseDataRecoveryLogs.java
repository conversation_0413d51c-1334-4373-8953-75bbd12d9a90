package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseDataRecoveryLogs<M extends BaseDataRecoveryLogs<M>> extends Model<M> implements IBean {

    public M setId(java.lang.Long id) {
        set("id", id);
        return (M)this;
    }
    
    public java.lang.Long getId() {
        return getLong("id");
    }

    public M setUserId(java.lang.Integer userId) {
        set("userId", userId);
        return (M)this;
    }
    
    public java.lang.Integer getUserId() {
        return getInt("userId");
    }

    public M setDataType(java.lang.String dataType) {
        set("dataType", dataType);
        return (M)this;
    }
    
    public java.lang.String getDataType() {
        return getStr("dataType");
    }

    public M setTargetId(java.lang.Integer targetId) {
        set("targetId", targetId);
        return (M)this;
    }
    
    public java.lang.Integer getTargetId() {
        return getInt("targetId");
    }

    public M setTargetName(java.lang.String targetName) {
        set("targetName", targetName);
        return (M)this;
    }
    
    public java.lang.String getTargetName() {
        return getStr("targetName");
    }

    public M setRecoveryType(java.lang.String recoveryType) {
        set("recoveryType", recoveryType);
        return (M)this;
    }
    
    public java.lang.String getRecoveryType() {
        return getStr("recoveryType");
    }

    public M setRecoveryStatus(java.lang.String recoveryStatus) {
        set("recoveryStatus", recoveryStatus);
        return (M)this;
    }
    
    public java.lang.String getRecoveryStatus() {
        return getStr("recoveryStatus");
    }

    public M setErrorMessage(java.lang.String errorMessage) {
        set("errorMessage", errorMessage);
        return (M)this;
    }
    
    public java.lang.String getErrorMessage() {
        return getStr("errorMessage");
    }

    public M setRecoveryTime(java.util.Date recoveryTime) {
        set("recoveryTime", recoveryTime);
        return (M)this;
    }
    
    public java.util.Date getRecoveryTime() {
        return get("recoveryTime");
    }

    public M setOriginalDeleteTime(java.util.Date originalDeleteTime) {
        set("originalDeleteTime", originalDeleteTime);
        return (M)this;
    }
    
    public java.util.Date getOriginalDeleteTime() {
        return get("originalDeleteTime");
    }

    public M setMetadata(java.lang.String metadata) {
        set("metadata", metadata);
        return (M)this;
    }
    
    public java.lang.String getMetadata() {
        return getStr("metadata");
    }
}
