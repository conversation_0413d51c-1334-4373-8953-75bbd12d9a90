package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JF<PERSON>, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseTempletGroupI18n<M extends BaseTempletGroupI18n<M>> extends Model<M> implements IBean {

	public M setId(java.lang.Integer id) {
		set("id", id);
		return (M)this;
	}
	
	public java.lang.Integer getId() {
		return getInt("id");
	}

	public M setLocale(java.lang.String locale) {
		set("locale", locale);
		return (M)this;
	}
	
	public java.lang.String getLocale() {
		return getStr("locale");
	}

	public M setTempletGroupId(java.lang.Integer templetGroupId) {
		set("templetGroupId", templetGroupId);
		return (M)this;
	}
	
	public java.lang.Integer getTempletGroupId() {
		return getInt("templetGroupId");
	}

	public M setTempletGroupName(java.lang.String templetGroupName) {
		set("templetGroupName", templetGroupName);
		return (M)this;
	}
	
	public java.lang.String getTempletGroupName() {
		return getStr("templetGroupName");
	}

}
