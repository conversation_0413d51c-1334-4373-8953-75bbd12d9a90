package com.sandu.xinye.api.app_version;

import com.jfinal.kit.StrKit;
import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.AppVersion;
import com.sandu.xinye.common.model.DownloadSet;

public class AppVersionService {

    public static final AppVersionService me = new AppVersionService();

    public RetKit getNewestVersion() {
        AppVersion model = getNewestModel();
        return RetKit.ok("model", model);
    }

    public String downloadApkUrl() {
        DownloadSet set = DownloadSet.dao.findFirst("select * from download_set limit 1");
        if (set == null) {
            return "";
        }
        String url = set.getUrl();
        return url;
    }

    private AppVersion getNewestModel() {
        AppVersion model = AppVersion.dao
                .findFirst("select id,androidVersion,androidApk,androidInfo from app_version order by id desc limit 1");
        return model;
    }
}
