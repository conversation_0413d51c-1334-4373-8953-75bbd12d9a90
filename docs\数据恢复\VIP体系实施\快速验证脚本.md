# 数据恢复VIP权限功能快速验证脚本

## 1. 数据库准备

### 1.1 执行数据库变更
```sql
-- 添加user_tier字段
ALTER TABLE `user` ADD COLUMN `user_tier` VARCHAR(20) NOT NULL DEFAULT 'FREE' COMMENT 'VIP等级：FREE/VIP_MONTHLY/VIP_YEARLY/VIP_LIFETIME';

-- 添加索引
CREATE INDEX `idx_user_tier` ON `user` (`user_tier`);

-- 验证字段添加成功
SELECT COLUMN_NAME, DATA_TYPE, COLUMN_DEFAULT, COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'user' AND COLUMN_NAME = 'user_tier';
```

### 1.2 创建测试用户
```sql
-- 创建或更新测试用户
-- 免费用户（userId = 1）
UPDATE user SET user_tier = 'FREE' WHERE userId = 1;

-- VIP月度用户（userId = 2）
UPDATE user SET user_tier = 'VIP_MONTHLY' WHERE userId = 2;

-- VIP年度用户（userId = 3）
UPDATE user SET user_tier = 'VIP_YEARLY' WHERE userId = 3;

-- 验证用户等级设置
SELECT userId, userNickName, user_tier FROM user WHERE userId IN (1, 2, 3);
```

## 2. 创建测试数据

### 2.1 创建不同时间的删除数据
```sql
-- 为用户1（免费用户）创建测试数据
-- 注意：需要根据实际的templet表结构调整字段

-- 1天前删除的模板（免费用户可恢复）
INSERT INTO templet (userId, name, data, deleteTime, createTime, updateTime) 
VALUES (1, '免费用户-1天前删除', '{}', DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 10 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY));

-- 5天前删除的模板（免费用户可恢复）
INSERT INTO templet (userId, name, data, deleteTime, createTime, updateTime) 
VALUES (1, '免费用户-5天前删除', '{}', DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_SUB(NOW(), INTERVAL 15 DAY), DATE_SUB(NOW(), INTERVAL 5 DAY));

-- 10天前删除的模板（免费用户不可恢复）
INSERT INTO templet (userId, name, data, deleteTime, createTime, updateTime) 
VALUES (1, '免费用户-10天前删除', '{}', DATE_SUB(NOW(), INTERVAL 10 DAY), DATE_SUB(NOW(), INTERVAL 20 DAY), DATE_SUB(NOW(), INTERVAL 10 DAY));

-- 30天前删除的模板（免费用户不可恢复）
INSERT INTO templet (userId, name, data, deleteTime, createTime, updateTime) 
VALUES (1, '免费用户-30天前删除', '{}', DATE_SUB(NOW(), INTERVAL 30 DAY), DATE_SUB(NOW(), INTERVAL 40 DAY), DATE_SUB(NOW(), INTERVAL 30 DAY));

-- 为用户2（VIP用户）创建测试数据
-- 30天前删除的模板（VIP用户可恢复）
INSERT INTO templet (userId, name, data, deleteTime, createTime, updateTime) 
VALUES (2, 'VIP用户-30天前删除', '{}', DATE_SUB(NOW(), INTERVAL 30 DAY), DATE_SUB(NOW(), INTERVAL 40 DAY), DATE_SUB(NOW(), INTERVAL 30 DAY));

-- 90天前删除的模板（VIP用户可恢复）
INSERT INTO templet (userId, name, data, deleteTime, createTime, updateTime) 
VALUES (2, 'VIP用户-90天前删除', '{}', DATE_SUB(NOW(), INTERVAL 90 DAY), DATE_SUB(NOW(), INTERVAL 100 DAY), DATE_SUB(NOW(), INTERVAL 90 DAY));

-- 200天前删除的模板（VIP用户不可恢复）
INSERT INTO templet (userId, name, data, deleteTime, createTime, updateTime) 
VALUES (2, 'VIP用户-200天前删除', '{}', DATE_SUB(NOW(), INTERVAL 200 DAY), DATE_SUB(NOW(), INTERVAL 210 DAY), DATE_SUB(NOW(), INTERVAL 200 DAY));
```

### 2.2 验证测试数据
```sql
-- 查看所有测试数据
SELECT userId, name, deleteTime, 
       DATEDIFF(NOW(), deleteTime) as days_ago,
       CASE 
         WHEN DATEDIFF(NOW(), deleteTime) <= 7 THEN '免费用户可恢复'
         WHEN DATEDIFF(NOW(), deleteTime) <= 180 THEN '仅VIP用户可恢复'
         ELSE '所有用户都不可恢复'
       END as recovery_status
FROM templet 
WHERE userId IN (1, 2) AND deleteTime IS NOT NULL
ORDER BY userId, deleteTime DESC;
```

## 3. API测试

### 3.1 测试权限信息接口

**免费用户权限查询：**
```bash
curl -X GET "http://localhost:8080/api/v2/datarecovery/getRecoveryPermission" \
  -H "Authorization: Bearer [免费用户token]" \
  -H "Content-Type: application/json"
```

**预期响应：**
```json
{
  "success": true,
  "data": {
    "hasPermission": true,
    "userTier": "FREE",
    "userTierDisplay": "免费用户",
    "recoveryPeriodDays": 7,
    "earliestRecoveryDate": "2024-07-15T00:00:00",
    "upgradeMessage": "升级VIP可恢复6个月内的数据",
    "upgradeRequired": false
  }
}
```

**VIP用户权限查询：**
```bash
curl -X GET "http://localhost:8080/api/v2/datarecovery/getRecoveryPermission" \
  -H "Authorization: Bearer [VIP用户token]" \
  -H "Content-Type: application/json"
```

**预期响应：**
```json
{
  "success": true,
  "data": {
    "hasPermission": true,
    "userTier": "VIP_MONTHLY",
    "userTierDisplay": "VIP月度",
    "recoveryPeriodDays": 180,
    "earliestRecoveryDate": "2024-01-22T00:00:00",
    "upgradeMessage": null,
    "upgradeRequired": false
  }
}
```

### 3.2 测试数据查询接口

**免费用户查询已删除数据：**
```bash
curl -X GET "http://localhost:8080/api/v2/datarecovery/getDeletedData?dataType=template&pageNumber=1&pageSize=10" \
  -H "Authorization: Bearer [免费用户token]" \
  -H "Content-Type: application/json"
```

**验证点：**
- 只应该返回7天内删除的数据
- 响应中应该包含权限信息

**VIP用户查询已删除数据：**
```bash
curl -X GET "http://localhost:8080/api/v2/datarecovery/getDeletedData?dataType=template&pageNumber=1&pageSize=10" \
  -H "Authorization: Bearer [VIP用户token]" \
  -H "Content-Type: application/json"
```

**验证点：**
- 应该返回180天内删除的数据
- 响应中应该包含VIP权限信息

### 3.3 测试数据恢复接口

**免费用户恢复7天内数据（应该成功）：**
```bash
curl -X POST "http://localhost:8080/api/v2/datarecovery/recoverSingleData" \
  -H "Authorization: Bearer [免费用户token]" \
  -H "Content-Type: application/json" \
  -d '{
    "dataType": "template",
    "dataId": "[1天前删除的模板ID]"
  }'
```

**免费用户恢复10天前数据（应该失败）：**
```bash
curl -X POST "http://localhost:8080/api/v2/datarecovery/recoverSingleData" \
  -H "Authorization: Bearer [免费用户token]" \
  -H "Content-Type: application/json" \
  -d '{
    "dataType": "template",
    "dataId": "[10天前删除的模板ID]"
  }'
```

**预期错误响应：**
```json
{
  "success": false,
  "msg": "数据超出可恢复时间范围，请升级VIP获得更长恢复期限"
}
```

## 4. 验证清单

### 4.1 数据库验证
- [ ] user_tier字段已添加
- [ ] 索引已创建
- [ ] 测试用户等级已设置
- [ ] 测试数据已创建

### 4.2 API功能验证
- [ ] 免费用户权限信息正确
- [ ] VIP用户权限信息正确
- [ ] 免费用户只能查看7天内数据
- [ ] VIP用户可以查看180天内数据
- [ ] 免费用户恢复超期数据被拒绝
- [ ] VIP用户可以恢复180天内数据
- [ ] 权限信息包含在API响应中

### 4.3 边界条件验证
- [ ] 恰好7天的数据（免费用户边界）
- [ ] 恰好180天的数据（VIP用户边界）
- [ ] 用户等级变更后权限立即生效
- [ ] 无效的数据ID处理
- [ ] 其他用户数据访问权限

## 5. 问题排查

### 5.1 常见问题
1. **数据库字段未添加**
   - 检查SQL执行是否成功
   - 验证字段是否存在

2. **权限检查不生效**
   - 检查用户等级是否正确设置
   - 验证VipPermissionService是否正常工作

3. **时间计算错误**
   - 检查时区设置
   - 验证日期计算逻辑

### 5.2 调试方法
1. **查看日志**
   - 检查应用日志中的权限检查信息
   - 查看数据库查询日志

2. **数据库直接查询**
   ```sql
   -- 检查用户等级
   SELECT userId, user_tier FROM user WHERE userId = ?;
   
   -- 检查删除数据的时间分布
   SELECT 
     DATEDIFF(NOW(), deleteTime) as days_ago,
     COUNT(*) as count
   FROM templet 
   WHERE userId = ? AND deleteTime IS NOT NULL
   GROUP BY DATEDIFF(NOW(), deleteTime)
   ORDER BY days_ago;
   ```

## 6. 清理测试数据

```sql
-- 清理测试数据（可选）
DELETE FROM templet WHERE name LIKE '%测试%' OR name LIKE '%免费用户%' OR name LIKE '%VIP用户%';

-- 重置用户等级（可选）
UPDATE user SET user_tier = 'FREE' WHERE userId IN (1, 2, 3);
```
