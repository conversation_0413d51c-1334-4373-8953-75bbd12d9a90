dbType=mysql

##Xprinter本地数据库配置(端口：8888)
jdbcUrl = ***************************************************************************************************************************
user=xprinter_test
password=Vida@2022

##短信签名id
##正式版(端口：8888)
signId=105174
##文件目录
uploadPath=D:/workspace/xprinter-backend/upload/
##ip地址
uploadIp=http://*************:8080/upload
##拦截器限流（每分钟最大访问次数）
maxAccessPerMinute=2

##阿里oss sts
imgBucketName=xprinter-private
stsTokenServer=http://************:7080

##阿里商品条码查询查询信息接口配置
barcode.host=https://ali-barcode.showapi.com
barcode.path=/barcode
barcode.method=GET
barcode.appcode=12345678
