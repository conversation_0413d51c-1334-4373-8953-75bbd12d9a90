package com.sandu.xinye.common.validator;

import java.util.List;
import java.util.Map;

import com.jfinal.core.Controller;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.upload.UploadFile;
import com.jfinal.validate.Validator;
import com.sandu.xinye.common.controller.BaseController;
import com.sandu.xinye.common.kit.RetKit;

/**
 * 基础拦截扩张
 *
 * <AUTHOR>
 */
public abstract class BaseValidator extends Validator {

    protected String erroKey = "msg";
    //身份证15位数
    private static final String REGEX_ID_CARD_15 = "^[1-9]\\d{7}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}$";
    //身份证 18位
    private static final String REGEX_ID_CARD_18 = "^[1-9]\\d{5}[1-9]\\d{3}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}([0-9]|X)$";
    //密码
    private static final String REGEX_PASSWORD = "^(?=.*\\d)(?=.*[a-z])(?=.*[A-Z]).{8,10}$";
    //URL
    private static final String REGEX_URL = "http(s)?://([\\w-]+\\.)+[\\w-]+(/[\\w- ./?%&=]*)?";
    //IP地址
    private static final String REGEX_IP_ADDR = "(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)";
    //汉字
    public static final String REGEX_CHINESE = "^[\u4e00-\u9fa5],{0,}$";
    //手机号 17 14 13 15开头
    public static final String REGEX_MOBILE = "^1[3456789]\\d{9}$";

    @Override
    protected abstract void validate(Controller c);

    @Override
    protected abstract void handleError(Controller c);

    protected void renderErroJson(Controller c) {
        c.renderJson(RetKit.fail(c.getAttr(erroKey)));
    }

    protected void renderErroJson(Controller c, int errorCode) {
        c.renderJson(RetKit.fail(errorCode, c.getAttr(erroKey)));
    }

    /**
     * 验证字段长度
     *
     * @param field
     * @param length
     * @param errorKey
     * @param errorMessage
     */
    protected void validateLength(String field, int length, String errorKey, String errorMessage) {
        String value = controller.getPara(field);
        if (StrKit.notBlank(value) && value.length() > length) {
            addError(errorKey, errorMessage);
        }
    }

    /**
     * 验证字段范围长度
     *
     * @param field
     * @param startLength
     * @param endLength
     * @param errorKey
     * @param errorMessage
     */
    protected void validateBetweenLength(String field, int startLength, int endLength, String errorKey,
                                         String errorMessage) {
        String value = controller.getPara(field);
        if (value.length() > endLength || value.length() < startLength) {
            addError(errorKey, errorMessage);
        }
    }

    /**
     * 验证全部字段不为空
     *
     * @param errorKey
     * @param errorMessage
     */
    protected void validateAllParaNotNull(String errorKey, String errorMessage) {
        Map<String, String[]> paras = controller.getParaMap();
        for (Map.Entry<String, String[]> enty : paras.entrySet()) {
            String value = enty.getValue()[0];
            if (StrKit.isBlank(value)) {
                addError(errorKey, errorMessage);
            }
        }
    }

    /**
     * 验证除了指定字段其他字段不能为空
     *
     * @param errorKey
     * @param errorMessage
     * @param para
     */
    protected void validateParaNotNullExceptParas(String errorKey, String errorMessage, String... para) {
        Map<String, String[]> paras = controller.getParaMap();
        for (Map.Entry<String, String[]> enty : paras.entrySet()) {
            boolean isContainKey = true;
            String key = enty.getKey();
            for (String string : para) {
                if (key.equals(string)) {
                    isContainKey = false;
                }
            }
            if (isContainKey) {
                String value = enty.getValue()[0];
                if (StrKit.isBlank(value)) {
                    addError(errorKey, errorMessage);
                }
            } else {
                continue;
            }

        }
    }

    /**
     * 验证指定字段不能为空
     * @param para
     */
    protected void validateParaNotNullParas(String... para) {
        for (String field : para) {
            if (StrKit.isBlank(controller.getPara(field))) {
                addError(erroKey, field + "不能为空");
            }
        }
    }

    /**
     * 验证指定字段不能为空
     *
     * @param errorMessage
     * @param para
     */
    protected void validateParaNotNull(String para, String errorMessage) {
        if (StrKit.isBlank(controller.getPara(para))) {
            addError(erroKey, errorMessage + "不能为空");
        }
    }

    /**
     * 验证密码强度
     *
     * @param field
     * @param errorKey
     * @param errorMessage
     */
    protected void validatePasswordStrength(String field, String errorKey, String errorMessage) {
        validateRegex(field, REGEX_PASSWORD, errorKey, errorMessage);
    }

    /**
     * 验证是否相等
     *
     * @param field
     * @param compareField
     * @param errorMessage
     */
    protected void validateEqual(String field, String compareField, String errorKey, String errorMessage) {
        validateEqualField(field, compareField, errorKey, errorMessage);
    }



    /**
     * 验证是否是网址
     *
     * @param field
     * @param errorKey
     * @param errorMessage
     */
    protected void validateIsUrl(String field, String errorKey, String errorMessage) {
        validateRegex(field, REGEX_URL, errorKey, errorMessage);
    }

    /**
     * 判断是否为18位身份证
     *
     * @param field
     * @param errorKey
     * @param errorMessage
     */
    protected void validateIs18IdCard(String field, String errorKey, String errorMessage) {
        validateRegex(field, REGEX_ID_CARD_18, errorKey, errorMessage);
    }

    /**
     * 判断是否为15位身份证
     *
     * @param field
     * @param errorKey
     * @param errorMessage
     */
    protected void validateIs15IdCard(String field, String errorKey, String errorMessage) {
        validateRegex(field, REGEX_ID_CARD_15, errorKey, errorMessage);
    }

    /**
     * 验证IP地址
     *
     * @param field
     * @param errorKey
     * @param errorMessage
     */
    protected void validateIpAddress(String field, String errorKey, String errorMessage) {
        validateRegex(field, REGEX_IP_ADDR, errorKey, errorMessage);
    }

    /**
     * 验证手机号17 18 15 13开头
     *
     * @param field
     * @param errorKey
     * @param errorMessage
     */
    protected void validatePhone(String field, String errorKey, String errorMessage) {
        validateRegex(field, REGEX_MOBILE, errorKey, errorMessage);
    }

    /**
     * 验证汉字
     *
     * @param field
     * @param errorKey
     * @param errorMessage
     */
    protected void validateChinese(String field, String errorKey, String errorMessage) {
        validateRegex(field, REGEX_CHINESE, errorKey, errorMessage);
    }

    /**
     * 验证文件
     */
    protected void validateFile() {
        List<UploadFile> list = controller.getFiles();
        if (list == null || list.size() == 0) {
            addError(erroKey, "上传文件不能为空");
        }
    }

    /**
     * 验证文件
     * methodName 默认方法名包含此参数的才验证上传文件是否为空，否则只做getFiles()处理，方便后续接参
     */
    protected void validateFile(String methodName) {
        List<UploadFile> list = controller.getFiles();
        if (getActionMethodName().contains(methodName)) {
            if (list == null || list.size() == 0) {
                addError(erroKey, "上传文件不能为空");
            }
        }
    }

    /**
     * 验证文件是否为空
     *
     * @param errorKey
     * @param errorMessage
     */
    protected void validateFile(String errorKey, String errorMessage) {
        List<UploadFile> list = controller.getFiles();
        if (list == null || list.size() == 0) {
            addError(errorKey, errorMessage);
        }
    }

    /**
     * 验证body参数不能为空
     *
     * @param para
     * @param errorMessage
     */
    protected void validateBodyParamNotNull(String para, String errorMessage) {
        Record bodyPara = ((BaseController) controller).getArgsRecord();
        Object value = bodyPara.get(para);
        if (value == null) {
            addError(erroKey, errorMessage + "不能为空");
        }
    }

    /**
     * 验证body参数是否为整数
     */
    protected void validateBodyParamInteger (String field, String errorKey, String errorMessage) {
        Record bodyPara = ((BaseController) controller).getArgsRecord();
        try {
            bodyPara.getInt(field);
        } catch (Exception var5) {
            this.addError(errorKey, errorMessage);
        }
    }
    private void validateIntegerValue(String value, String errorKey, String errorMessage) {
        if (StrKit.isBlank(value)) {
            this.addError(errorKey, errorMessage);
        } else {
            try {
                Integer.parseInt(value.trim());
            } catch (Exception var5) {
                this.addError(errorKey, errorMessage);
            }

        }
    }
    /**
     * 验证body参数的长度是否匹配
     */
    protected void validateBodyParamLength(String field, int length, String errorKey, String errorMessage) {
        Record bodyPara = ((BaseController) controller).getArgsRecord();
        String value = bodyPara.getStr(field);
        if (StrKit.notBlank(value) && value.length() > length) {
            addError(errorKey, errorMessage);
        }
    }


}
