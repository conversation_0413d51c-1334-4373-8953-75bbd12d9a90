package com.sandu.xinye.api.v2.logo;

import com.jfinal.aop.Clear;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.kit.RetKit;

public class LogoController extends AppController {

    @Clear
    public void getLogoList() {
        Integer language = getParaToInt("language");
        RetKit ret = LogoService.me.getLogoList(language);
        renderJson(ret);
    }


    /**
     * @description 获取logo列表分页
     * <AUTHOR>
     * @date 2022/7/27 9:31
     */
    public void getLogoKindList() {
        Integer language = getParaToInt("language");
        RetKit ret = LogoService.me.getLogoKindList(language);
        renderJson(ret);
    }


    /**
     * @description 获取logo列表分页
     * <AUTHOR>
     * @date 2022/7/27 9:31
     */
    public void getLogoPage() {
        int pageNumber = getParaToInt("pageNumber", 1);
        int pageSize = getParaToInt("pageSize", 10);
        int language = getParaToInt("language", 1);
        String kindId = getPara("logoKindId");
        System.out.println(String.format("pageNumber:%s, pageSize: %s, language: %s, groupId: %s", pageNumber, pageSize, language, kindId));
        RetKit ret = LogoService.me.getLogoPage(pageNumber, pageSize, kindId, language);
        renderJson(ret);
    }
}
