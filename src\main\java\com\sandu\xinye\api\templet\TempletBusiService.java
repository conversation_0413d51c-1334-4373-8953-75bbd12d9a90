package com.sandu.xinye.api.templet;

import com.jfinal.kit.Kv;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.SqlPara;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.Templet;
import com.sandu.xinye.common.model.TempletGroup;
import java.util.List;

public class TempletBusiService {
    private static final org.apache.log4j.Logger logger = org.apache.log4j.Logger.getLogger(TempletBusiService.class);

    public static final TempletBusiService me = new TempletBusiService();

    public RetKit getTempletPage(int pageNumber, int pageSize, String name, String groupId, String indexRange) {
        if (StrKit.notBlank(name)) {
            name = "%" + name + "%";
        }
        Kv kvParams = Kv.by("groupId", groupId).set("name", name);
        if (StrKit.notBlank(indexRange)) {
            String[] indexRangeArr = indexRange.split(",");
            kvParams.set("widthBegin", indexRangeArr[0]);
            if (indexRangeArr.length > 1) {
                kvParams.set("widthEnd", indexRangeArr[1]);
            }
        }
        SqlPara sqlPara = Db.getSqlPara("app.templet.busi.paginate", kvParams);

        Page<Templet> page = Templet.dao.paginate(pageNumber, pageSize, sqlPara);
        return RetKit.ok("page", page);
    }

    public RetKit getGroupList() {
        List<TempletGroup> list = getBusiTemplateGroupList();
        return RetKit.ok("list", list);
    }

    private List<TempletGroup> getBusiTemplateGroupList() {
        List<TempletGroup> list = TempletGroup.dao.find("select id as groupId,name from templet_group where type = 1 order by createTime  asc");
        return list;
    }

}
