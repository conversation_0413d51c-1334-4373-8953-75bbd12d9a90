package com.sandu.xinye.api.v2.common;

import com.jfinal.aop.Before;
import com.jfinal.aop.Clear;
import com.jfinal.kit.LogKit;
import com.jfinal.upload.UploadFile;
import com.sandu.xinye.admin.upload.UploadService;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.interceptor.AttackAntiInterceptor;
import com.sandu.xinye.common.interceptor.I18nInterceptor;
import com.sandu.xinye.common.interceptor.RateLimitInterceptor;
import com.sandu.xinye.common.kit.RetKit;
import com.jfinal.kit.StrKit;
import com.jfinal.kit.PropKit;

public class CommonController extends AppController {
    @Clear
    public void healthCheck() {
        renderNull();
    }

    @Clear
    @Before({ AttackAntiInterceptor.class, RateLimitInterceptor.class, I18nInterceptor.class })
    public void sendCaptcha() {
        LogKit.info("调用发送验证码接口开始-----------------" + getParaToMap().toJson());
        // IP黑名单检查
        String ip = getIpAddress();
        LogKit.info("IP:" + ip);
        String blacklist = PropKit.use("common_config.txt").get("sms_ip_blacklist", "");
        if (blacklist != null && !blacklist.isEmpty()) {
            String[] blackIps = blacklist.split(",");
            for (String blackIp : blackIps) {
                if (ip.trim().equals(blackIp.trim())) {
                    renderJson(RetKit.fail("当前IP已被禁用，无法发送短信"));
                    return;
                }
            }
        }
        String phone = getPara("phone");
        String type = getPara("type");
        Boolean isInternational = getParaToBoolean("intern", false);
        String sign = getHeader("sign");
        LogKit.info("sign:" + sign);
        RetKit ret;
        if (!StrKit.isBlank(phone)) {
            ret = com.sandu.xinye.api.v2.common.CommonService.me.sendCaptcha(phone, type, isInternational, sign);
        } else {
            String email = getPara("email");
            ret = com.sandu.xinye.api.v2.common.CommonService.me.sendEmailCaptcha(email, type);
        }
        renderJson(ret);
        LogKit.info("调用发送验证码接口结束-----------------");
    }

    @Clear
    @Before({ I18nInterceptor.class })
    public void checkCaptcha() {
        String phone = getPara("phone");
        String captchaKey = phone;
        if (StrKit.isBlank(phone)) {
            // 如果没有传phone参数，获取email参数
            captchaKey = getPara("email");
        }
        String captcha = getPara("captcha");
        RetKit ret = CommonService.me.checkCaptcha(captchaKey, captcha);
        renderJson(ret);
    }

    public void uploadImg() {
        UploadFile uf = null;
        try {
            uf = getFile("file");
        } catch (Exception e) {
            LogKit.error("上传文件失败！" + e.getMessage());
        }

        RetKit ret = UploadService.me.uploadImg(uf);
        renderJson(ret);
    }
}
