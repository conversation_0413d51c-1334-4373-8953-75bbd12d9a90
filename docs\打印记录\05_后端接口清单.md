# 打印记录功能后端接口开发清单

## 1. 核心API接口

根据产品经理需求分析，后端需要开发以下接口：

### 1.1 打印记录管理接口

| 序号 | 接口名称 | 请求方法 | 接口路径 | 功能描述 | 优先级 |
|------|----------|----------|----------|----------|--------|
| 1 | 获取模板打印记录列表 | GET | `/api/v2/printRecord/template/list` | 获取用户的模板打印记录，支持分页，默认100条 | 高 |
| 2 | 获取文档打印记录列表 | GET | `/api/v2/printRecord/document/list` | 获取用户的文档打印记录，支持分页，默认20条 | 中 |
| 3 | 获取图片打印记录列表 | GET | `/api/v2/printRecord/image/list` | 获取用户的图片打印记录，支持分页，默认20条 | 中 |
| 4 | 搜索模板打印记录 | GET | `/api/v2/printRecord/template/search` | 按名称、尺寸搜索模板打印记录 | 高 |
| 5 | 搜索文档打印记录 | GET | `/api/v2/printRecord/document/search` | 按名称搜索文档打印记录 | 中 |
| 6 | 搜索图片打印记录 | GET | `/api/v2/printRecord/image/search` | 按名称搜索图片打印记录 | 中 |
| 7 | 删除打印记录 | POST | `/api/v2/printRecord/delete` | 软删除指定的打印记录 | 高 |
| 8 | 获取打印记录详情 | GET | `/api/v2/printRecord/detail/{id}` | 获取单条打印记录的详细信息 | 中 |

### 1.2 打印操作集成接口

| 序号 | 接口名称 | 请求方法 | 接口路径 | 功能描述 | 优先级 |
|------|----------|----------|----------|----------|--------|
| 9 | 保存模板打印记录 | POST | `/api/v2/printRecord/template/save` | 模板打印时保存打印记录 | 高 |
| 10 | 保存文档打印记录 | POST | `/api/v2/printRecord/document/save` | 文档打印时保存打印记录 | 中 |
| 11 | 保存图片打印记录 | POST | `/api/v2/printRecord/image/save` | 图片打印时保存打印记录 | 中 |

## 2. 接口详细规格

### 2.1 获取模板打印记录列表

**接口路径：** `GET /api/v2/printRecord/template/list`

**请求参数：**
```json
{
  "pageNumber": 1,       // 可选，页码，默认1
  "pageSize": 100        // 可选，页大小，默认100
}
```

**响应数据：**
```json
{
  "state": "ok",
  "data": {
    "list": [
      {
        "id": 1,
        "templateId": 123,
        "templateName": "商品标签模板",
        "templateCover": "http://example.com/cover.jpg",
        "printWidth": 50,
        "printHeight": 30,
        "printCopies": 5,
        "printPlatform": "手机iOS",
        "printTime": "2024-01-15 14:30:00"
      }
    ],
    "totalRow": 150,
    "pageNumber": 1,
    "pageSize": 100,
    "totalPage": 2
  }
}
```

### 2.2 搜索模板打印记录

**接口路径：** `GET /api/v2/printRecord/template/search`

**请求参数：**
```json
{
  "keyword": "商品",      // 可选，按模板名称搜索
  "width": 50,           // 可选，按打印宽度搜索
  "height": 30,          // 可选，按打印高度搜索
  "pageNumber": 1,       // 可选，页码，默认1
  "pageSize": 20         // 可选，页大小，默认20
}
```

### 2.3 删除打印记录

**接口路径：** `POST /api/v2/printRecord/delete`

**请求参数：**
```json
{
  "id": 123              // 必填，打印记录ID
}
```

**响应数据：**
```json
{
  "state": "ok",
  "msg": "删除成功"
}
```

### 2.4 保存模板打印记录

**接口路径：** `POST /api/v2/printRecord/template/save`

**请求参数：**
```json
{
  "templateId": 123,
  "templateName": "商品标签模板",
  "templateCover": "http://example.com/cover.jpg",
  "printWidth": 50,
  "printHeight": 30,
  "printCopies": 5,
  "printPlatform": 1,    // 1-iOS, 2-Android, 3-Windows, 4-Mac
  "templateData": "{...}" // 模板数据JSON
}
```

## 3. 数据交互说明

### 3.1 前端调用场景

1. **页面加载**：前端进入打印记录页面时，调用对应的列表接口获取数据
2. **搜索操作**：用户输入搜索条件时，调用搜索接口
3. **删除操作**：用户点击删除按钮时，弹窗确认后调用删除接口
4. **打印操作**：用户进行打印时，后端自动调用保存接口记录打印信息
5. **重新打印**：用户点击"打印"按钮时，使用记录中的数据重新打印
6. **编辑操作**：用户点击预览图时，跳转到编辑页面

### 3.2 与现有功能集成点

1. **模板打印功能**：在 `TempletService` 的打印方法中集成记录保存
2. **文档打印功能**：在文档打印相关服务中集成记录保存
3. **图片打印功能**：在图片打印相关服务中集成记录保存

## 4. 技术实现要点

### 4.1 数据库操作
- 使用JFinal的Model进行数据库操作
- 实现软删除机制（设置deleteTime字段）
- 优化分页查询性能

### 4.2 业务逻辑
- 用户权限验证：只能访问自己的打印记录
- 数据格式转换：将数据库字段转换为前端需要的格式
- 异常处理：处理各种异常情况并返回合适的错误信息

### 4.3 性能优化
- 数据库索引优化
- 分页查询优化
- 缓存策略（如果需要）

## 5. 开发优先级

### 5.1 第一期（必须完成）
- 模板打印记录的所有接口
- 基础的增删查功能
- 与现有模板打印功能的集成

### 5.2 第二期（后续版本）
- 文档打印记录功能
- 图片打印记录功能
- 高级搜索功能

### 5.3 第三期（优化版本）
- 性能优化
- 数据统计功能
- 批量操作功能

## 6. 错误处理

### 6.1 常见错误码
- 40001：参数错误
- 40002：记录不存在
- 40003：无权限访问
- 50001：服务器内部错误

### 6.2 异常场景处理
- 用户未登录：返回认证错误
- 记录不存在：返回记录不存在错误
- 权限不足：返回权限错误
- 数据库异常：返回服务器错误

## 7. 测试要点

### 7.1 功能测试
- 各接口的正常流程测试
- 边界条件测试
- 异常情况测试

### 7.2 性能测试
- 大数据量下的查询性能
- 并发访问测试
- 内存使用情况

### 7.3 集成测试
- 与现有打印功能的集成测试
- 端到端流程测试
