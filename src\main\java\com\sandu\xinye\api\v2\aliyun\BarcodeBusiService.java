package com.sandu.xinye.api.v2.aliyun;

import com.jfinal.kit.StrKit;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.ProductInfo;
import com.sandu.xinye.api.v2.aliyun.util.BarcodeUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

public class BarcodeBusiService {
    private static final Logger logger = LoggerFactory.getLogger(BarcodeBusiService.class);
    public static final BarcodeBusiService me = new BarcodeBusiService();

    private BarcodeBusiService() {
    }

    public RetKit queryProductInfo(String code) {
        logger.info("开始查询商品信息，条形码: {}", code);

        if (code == null || code.trim().isEmpty()) {
            logger.warn("条形码为空");
            return RetKit.fail("msg", "条形码不能为空");
        }

        // 1. 先从本地数据库查询
        logger.debug("从本地数据库查询商品信息");
        ProductInfo productInfo = ProductInfo.dao.findFirst("select * from product_info where code = ?", code);
        if (productInfo != null) {
            logger.info("在本地数据库中找到商品信息，id:" + productInfo.getId());
            return RetKit.ok("data", productInfo);
        }

        // 2. 本地没有，调用阿里云API查询
        logger.info("本地数据库未找到商品信息，调用阿里云API查询");
        BarcodeUtil.BarcodeResponse response = BarcodeUtil.queryBarcode(code);
        if (response == null) {
            logger.error("调用阿里云API查询失败");
            return RetKit.fail("msg", "查询失败，请稍后重试");
        }

        // 3. 解析响应
        if (response.getShowapi_res_code() != 0) {
            logger.warn("阿里云API返回错误，错误信息: {}", response.getShowapi_res_error());
            return RetKit.fail("msg", response.getShowapi_res_error());
        }

        BarcodeUtil.BarcodeBody body = response.getShowapi_res_body();
        if (!"true".equals(body.getFlag())) {
            logger.warn("阿里云API未找到商品信息，备注: {}", body.getRemark());
            return RetKit.fail("msg", body.getRemark());
        }
        // 判断商品名称是否为空
        if (StrKit.isBlank(body.getGoodsName())) {
            logger.warn("阿里云API未找到商品信息，备注: {}", body.getRemark());
            return RetKit.fail("msg", body.getRemark());
        }

        // 4. 保存到本地数据库
        logger.info("将查询到的商品信息保存到本地数据库");
        productInfo = new ProductInfo();
        productInfo.setCode(body.getCode());
        productInfo.setGoodsName(body.getGoodsName());
        productInfo.setManuName(body.getManuName());
        productInfo.setSpec(body.getSpec());
        productInfo.setPrice(body.getPrice());
        productInfo.setTrademark(body.getTrademark());
        productInfo.setImg(body.getImg());
        productInfo.setGoodsType(body.getGoodsType());
        productInfo.setSptmImg(body.getSptmImg());
        productInfo.setYcg(body.getYcg());
        productInfo.setEngName(body.getEngName());
        productInfo.setNote(body.getNote());
        productInfo.setCreateTime(new Date());
        productInfo.setUpdateTime(new Date());

        if (productInfo.save()) {
            logger.info("成功保存商品信息到本地数据库");
            return RetKit.ok("data", productInfo);
        } else {
            logger.error("保存商品信息到本地数据库失败");
            return RetKit.fail("msg", "保存商品信息失败");
        }
    }
}