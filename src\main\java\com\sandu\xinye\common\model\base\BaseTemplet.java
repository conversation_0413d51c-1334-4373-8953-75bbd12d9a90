package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseTemplet<M extends BaseTemplet<M>> extends Model<M> implements IBean {

	public M setId(java.lang.Integer id) {
		set("id", id);
		return (M)this;
	}
	
	public java.lang.Integer getId() {
		return getInt("id");
	}

	public M setUserId(java.lang.Integer userId) {
		set("userId", userId);
		return (M)this;
	}
	
	public java.lang.Integer getUserId() {
		return getInt("userId");
	}

	public M setGroupId(java.lang.Integer groupId) {
		set("groupId", groupId);
		return (M)this;
	}
	
	public java.lang.Integer getGroupId() {
		return getInt("groupId");
	}

	public M setName(java.lang.String name) {
		set("name", name);
		return (M)this;
	}
	
	public java.lang.String getName() {
		return getStr("name");
	}

	public M setCover(java.lang.String cover) {
		set("cover", cover);
		return (M)this;
	}
	
	public java.lang.String getCover() {
		return getStr("cover");
	}

	public M setGap(java.lang.Float gap) {
		set("gap", gap);
		return (M)this;
	}
	
	public java.lang.Float getGap() {
		return getFloat("gap");
	}

	public M setHeight(java.lang.Integer height) {
		set("height", height);
		return (M)this;
	}
	
	public java.lang.Integer getHeight() {
		return getInt("height");
	}

	public M setWidth(java.lang.Integer width) {
		set("width", width);
		return (M)this;
	}
	
	public java.lang.Integer getWidth() {
		return getInt("width");
	}

	public M setPaperType(java.lang.Integer paperType) {
		set("paperType", paperType);
		return (M)this;
	}
	
	public java.lang.Integer getPaperType() {
		return getInt("paperType");
	}

	public M setPrintDirection(java.lang.Integer printDirection) {
		set("printDirection", printDirection);
		return (M)this;
	}
	
	public java.lang.Integer getPrintDirection() {
		return getInt("printDirection");
	}

	public M setData(java.lang.String data) {
		set("data", data);
		return (M)this;
	}
	
	public java.lang.String getData() {
		return getStr("data");
	}

	public M setBlackLabelGap(java.lang.Float blackLabelGap) {
		set("blackLabelGap", blackLabelGap);
		return (M)this;
	}
	
	public java.lang.Float getBlackLabelGap() {
		return getFloat("blackLabelGap");
	}

	public M setBlackLabelOffset(java.lang.Float blackLabelOffset) {
		set("blackLabelOffset", blackLabelOffset);
		return (M)this;
	}
	
	public java.lang.Float getBlackLabelOffset() {
		return getFloat("blackLabelOffset");
	}

	public M setCreateTime(java.util.Date createTime) {
		set("createTime", createTime);
		return (M)this;
	}
	
	public java.util.Date getCreateTime() {
		return get("createTime");
	}

	public M setType(java.lang.Integer type) {
		set("type", type);
		return (M)this;
	}
	
	public java.lang.Integer getType() {
		return getInt("type");
	}

	public M setNameEn(java.lang.String nameEn) {
		set("nameEn", nameEn);
		return (M)this;
	}
	
	public java.lang.String getNameEn() {
		return getStr("nameEn");
	}

	public M setNameKor(java.lang.String nameKor) {
		set("nameKor", nameKor);
		return (M)this;
	}
	
	public java.lang.String getNameKor() {
		return getStr("nameKor");
	}

	public M setUpdateTime(java.util.Date updateTime) {
		set("updateTime", updateTime);
		return (M)this;
	}
	
	public java.util.Date getUpdateTime() {
		return get("updateTime");
	}

	public M setMachineType(java.lang.Integer machineType) {
		set("machineType", machineType);
		return (M)this;
	}
	
	public java.lang.Integer getMachineType() {
		return getInt("machineType");
	}

	public M setCutAfterPrint(java.lang.Integer cutAfterPrint) {
		set("cutAfterPrint", cutAfterPrint);
		return (M)this;
	}
	
	public java.lang.Integer getCutAfterPrint() {
		return getInt("cutAfterPrint");
	}

	public M setLabelNum(java.lang.Integer labelNum) {
		set("labelNum", labelNum);
		return (M)this;
	}
	
	public java.lang.Integer getLabelNum() {
		return getInt("labelNum");
	}

	public M setLabelGap(java.lang.Float labelGap) {
		set("labelGap", labelGap);
		return (M)this;
	}
	
	public java.lang.Float getLabelGap() {
		return getFloat("labelGap");
	}

	public M setShareUser(java.lang.Integer shareUser) {
		set("shareUser", shareUser);
		return (M)this;
	}
	
	public java.lang.Integer getShareUser() {
		return getInt("shareUser");
	}

	public M setMultiLabelType(java.lang.Integer multiLabelType) {
		set("multiLabelType", multiLabelType);
		return (M)this;
	}
	
	public java.lang.Integer getMultiLabelType() {
		return getInt("multiLabelType");
	}

	public M setPaperTearType(java.lang.Integer paperTearType) {
		set("paperTearType", paperTearType);
		return (M)this;
	}
	
	public java.lang.Integer getPaperTearType() {
		return getInt("paperTearType");
	}

	public M setLabelType(java.lang.Integer labelType) {
		set("labelType", labelType);
		return (M)this;
	}
	
	public java.lang.Integer getLabelType() {
		return getInt("labelType");
	}

	public M setEancode(java.lang.String eancode) {
		set("eancode", eancode);
		return (M)this;
	}
	
	public java.lang.String getEancode() {
		return getStr("eancode");
	}

}
