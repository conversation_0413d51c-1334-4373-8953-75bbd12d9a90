package com.sandu.xinye.api.v2.help;

import com.alibaba.druid.sql.visitor.functions.Concat;
import com.jfinal.kit.StrKit;
import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.Help;

import java.util.List;

public class HelpService {

    public static final HelpService me = new HelpService();


    /**
     * @param helpKind
     * @return
     * @Title: getHelp
     * @Description:
     * @date 2019年3月13日  上午10:53:42
     * <AUTHOR>
     */
    public RetKit getHelp(int helpKind) {
        List<Help> list = Help.dao.find("select * from help where helpKind = ?  order by createTime desc", helpKind);
        return RetKit.ok("list", list);
    }

    /**
     * 国际化查询
     *
     * @param helpKind
     * @return
     */
    public RetKit getHelpI18n(int helpKind, String locale) {
        if (StrKit.isBlank(locale) || locale.equals(Constant.LOCALE_ZH_CN)) {
            return getHelp(helpKind);
        }

        List<Help> list = Help.dao.find("select hp.helpId, hp.machineId, hp.helpLogo, hp.helpKind, hp.sysUserId, hp.createTime, " +
                " ifnull(i18n.helpAnswer, hp.helpAnswer) as helpAnswer, \n" +
                " ifnull(i18n.helpName, hp.helpName) as helpName,\n" +
                " ifnull(i18n.helpVideo, hp.helpVideo) as helpVideo,\n" +
                " ifnull(i18n.link, hp.link) as link " +
                " from help hp" +
                " left join help_i18n i18n on i18n.helpId = hp.helpId and i18n.locale = ?" +
                " where hp.helpKind = ? " +
                " order by hp.createTime desc", locale, helpKind);
        return RetKit.ok("list", list);
    }

}
