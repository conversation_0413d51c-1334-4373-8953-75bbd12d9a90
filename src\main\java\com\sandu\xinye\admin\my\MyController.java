package com.sandu.xinye.admin.my;

import com.sandu.xinye.common.controller.AdminController;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.SysUser;

public class MyController extends AdminController {

	public void resetPassword(){
		String oldPassword = getPara("oldPassword");
		String newPassword = getPara("password");
		RetKit ret = MyService.me.resetPassword(oldPassword, newPassword, getAccount(),getIpAddress());
		renderJson(ret);
	}

}
