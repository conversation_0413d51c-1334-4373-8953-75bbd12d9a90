package com.sandu.xinye.admin.dataBase;

import java.io.File;
import java.net.URLDecoder;
import java.util.Date;
import java.util.List;

import com.jfinal.kit.Kv;
import com.jfinal.kit.LogKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.SqlPara;
import com.sandu.xinye.admin.operate.OperationLogService;
import com.sandu.xinye.common.kit.AliOssKit;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.kit.UploadFileMoveKit;
import com.sandu.xinye.common.model.Logo;
import com.sandu.xinye.common.model.LogoChildKind;
import com.sandu.xinye.common.model.LogoKind;
import com.sandu.xinye.common.model.SysUser;

public class LogoService {
    public static final LogoService me = new LogoService();
    private static final String FILE_PATH = "logo";

    public RetKit list(int pageNumber, int pageSize, Kv kv) {
        SqlPara sqlPara = Db.getSqlPara("admin.logo.paginate", kv);
        Page<Logo> page = Logo.dao.paginate(pageNumber, pageSize, sqlPara);
        return RetKit.ok("page", page);
    }

    /**
     * 添加logo
     *
     * @param logo
     */
    public RetKit add(Logo logo, SysUser sysUser, String ip) {
        //子分类没用到，先设为0
        boolean succ = logo.setLogoChildKindId(0).setSysUserId(sysUser.getSysUserId()).setVersion(2).setCreateTime(new Date()).save();
        if (succ) {
            LogoKind lk = LogoKind.dao.findById(logo.getLogoKindId());
            String content = sysUser.getSysUserName() + "添加了" + lk.getLogoKindName() + "logo";
            OperationLogService.me.saveOperationLog(sysUser.getSysUserId(), ip, content);
        }
        return succ ? RetKit.ok() : RetKit.fail();
    }

    /**
     * 批量添加logo
     *
     * @param logoKindId
     * @param logoImg
     */
    public RetKit batchAdd(Integer logoKindId, List<String> logoImg, SysUser sysUser, String ip) {
        if (logoImg == null || logoImg.size() == 0) {
            return RetKit.fail("logo图片不能为空！");
        }
        boolean succ = true;
        try {
            for (String img : logoImg) {
                Logo logo = new Logo();
                logo.setLogoKindId(logoKindId);
                logo.setLogoImg(img);
                add(logo, sysUser, ip);
            }
        } catch (Exception ex) {
            succ = false;
        }
        return succ ? RetKit.ok().setMsg("logo批量上传成功！") : RetKit.fail("logo批量上传失败！");
    }

    public RetKit update(Logo logo, SysUser sysUser, String ip) {
        //子分类没用到，先设为0
        boolean succ = logo.setLogoChildKindId(0).setSysUserId(sysUser.getSysUserId()).setCreateTime(new Date()).update();
        if (succ) {
            LogoKind lk = LogoKind.dao.findById(logo.getLogoKindId());
            String content = sysUser.getSysUserName() + "编辑了" + lk.getLogoKindName() + "logo";
            OperationLogService.me.saveOperationLog(sysUser.getSysUserId(), ip, content);
        }
        return succ ? RetKit.ok() : RetKit.fail();
    }

    public RetKit del(String logoId, SysUser sysUser, String ip) {
        Logo logo = Logo.dao.findById(logoId);
        boolean succ = logo.delete();
        if (succ) {
            LogoKind lk = LogoKind.dao.findById(logo.getLogoKindId());
            String content = sysUser.getSysUserName() + "删除了" + lk.getLogoKindName() + "logo";
            OperationLogService.me.saveOperationLog(sysUser.getSysUserId(), ip, content);
        }
        return succ ? RetKit.ok() : RetKit.fail();
    }

}
