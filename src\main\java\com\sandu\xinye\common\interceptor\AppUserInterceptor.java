package com.sandu.xinye.common.interceptor;

import javax.servlet.http.HttpServletRequest;

import com.jfinal.aop.Interceptor;
import com.jfinal.aop.Invocation;
import com.jfinal.core.Controller;
import com.jfinal.i18n.I18n;
import com.jfinal.i18n.Res;
import com.jfinal.kit.StrKit;
import com.jfinal.render.JsonRender;
import com.jfinal.render.Render;
import com.sandu.xinye.api.login.UserLoginService;
import com.sandu.xinye.api.v2.operate.OperationLogService;
import com.sandu.xinye.common.annotation.OperationLog;
import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.constant.RetConstant;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.User;
import com.xiaoleilu.hutool.json.JSONObject;
import com.xiaoleilu.hutool.json.JSONUtil;
import org.apache.log4j.Logger;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import com.jfinal.kit.PropKit;
import java.util.Locale;

public class AppUserInterceptor implements Interceptor {
    private static final Logger logger = Logger.getLogger(I18nInterceptor.class);
    private static final Map LOCALE_MAP = new HashMap();

    static {
        LOCALE_MAP.put("en-US", String.valueOf(Locale.US));
        LOCALE_MAP.put("zh-CN", String.valueOf(Locale.SIMPLIFIED_CHINESE));
        LOCALE_MAP.put("zh-HK", "zh_HK");
        LOCALE_MAP.put("ko-KR", String.valueOf(Locale.KOREA));
        LOCALE_MAP.put("ja-JP", String.valueOf(Locale.JAPAN));
    }

    @Override
    public void intercept(Invocation inv) {
        Method method = inv.getMethod();
        Controller con = inv.getController();
        con.getClass().getDeclaredMethods();
        String sessionId = con.getHeader(Constant.APP_ACCESSTOKE);
        String platform = con.getHeader("platform");

        if (StrKit.isBlank(sessionId)) {
            con.renderJson(RetKit.fail(RetConstant.CODE_NO_LOGIN, "未登录！"));
            return;
        }
        if (StrKit.isBlank(platform)) {
            con.renderJson(RetKit.fail(RetConstant.CODE_NO_LOGIN, "用户platform不存在，请检查参数！"));
            return;
        }
        User user = UserLoginService.me.getUserCacheWithSessionId(sessionId);
        if (user == null) {
            user = UserLoginService.me.loginWithSessionId(sessionId, getIpAddress(con.getRequest()),
                    Integer.valueOf(platform));
        }
        if (user == null) {
            con.renderJson(RetKit.fail(RetConstant.CODE_LOGIN_EXPIRE, "Session已过期，请重新登录！"));
            return;
        }

        String locale = con.getHeader("Accept-Language");
//        logger.info("-----------> Accept-Language : " + locale);
        if (locale == null) {
            inv.invoke();
            return;
        }

        locale = locale.split(",")[0];
        if (LOCALE_MAP.containsKey(locale)) {
            locale = (String) LOCALE_MAP.get(locale);
        } else {
            locale = PropKit.get("i18n.defaultLocale");
        }

        inv.invoke();

        // 加入操作日志记录
        // 以注解标识
        boolean hasAnnotation = method.isAnnotationPresent(OperationLog.class);
        if (hasAnnotation) {
            OperationLog logBean = method.getAnnotation(OperationLog.class);
            String logContent = String.format("用户 %s 调用了接口 %s/%s",
                    user.getUserNickName(), con.getControllerKey(), method.getName());

            Render r = inv.getController().getRender();
            if (r instanceof JsonRender) {
                JSONObject ret = JSONUtil.parseObj(((JsonRender) r).getJsonText());
                if (ret.containsKey("success") && ret.getBool("success").equals(true)) {
                    OperationLogService.me.saveOperationLog(user.getUserId(), getIpAddress(con.getRequest()), logBean.modelName(), logContent);
                }
            }
        }

        // i18n
        Render r = inv.getController().getRender();
        if (r instanceof JsonRender) {
            String JsonText = ((JsonRender) r).getJsonText();
            // JsonText 处理
            JSONObject jsonObject = JSONUtil.parseObj(JsonText);
            try {
                if (StrKit.isBlank(locale)) {
                    locale = "zh_CN";
                }
                Res resLocale = I18n.use(locale);
//                logger.info("-----------> i18n " + locale);
                String msg = (String) jsonObject.get("msg");
//                logger.info("-----------> origin msg " + msg);
                String localeMsg = resLocale.get(msg);
                if (resLocale != null && localeMsg != null) {
//                    logger.info("-----------> locale msg " + localeMsg);
                    jsonObject.put("msg", localeMsg);
                    inv.getController().renderText(JSONUtil.toJsonStr(jsonObject));
                }
            } catch (Exception e) {
                logger.error(e.getMessage());
            }

        }

    }

    private String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("X-requested-For");
        if (StrKit.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Forwarded-For");
        }
        if (StrKit.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (StrKit.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (StrKit.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (StrKit.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (StrKit.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        if (ip != null && ip.contains(",")) {
            String[] ips = ip.split(",");
            for (int index = 0; index < ips.length; index++) {
                String strIp = ips[index];
                if (!("unknown".equalsIgnoreCase(strIp))) {
                    ip = strIp;
                    break;
                }
            }
        }

        return ip;
    }

}
