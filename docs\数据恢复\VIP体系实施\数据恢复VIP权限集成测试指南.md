# XPrinter数据恢复VIP权限集成测试指南

## 1. 实施概述

### 1.1 已完成的功能
- ✅ 创建VIP相关枚举和基础类
- ✅ 扩展用户表结构（添加user_tier字段）
- ✅ 创建VIP权限服务
- ✅ 修改数据恢复服务集成权限检查
- ✅ 添加权限信息API接口

### 1.2 核心功能
- **免费用户**：只能恢复7天内删除的数据
- **VIP用户**：可以恢复6个月内删除的数据
- **权限检查**：在查询和恢复时都会验证时间权限
- **升级提示**：为免费用户提供VIP升级引导

## 2. 数据库变更

### 2.1 执行SQL脚本
```sql
-- 1. 添加user_tier字段
ALTER TABLE `user` ADD COLUMN `user_tier` VARCHAR(20) NOT NULL DEFAULT 'FREE' COMMENT 'VIP等级：FREE/VIP_MONTHLY/VIP_YEARLY/VIP_LIFETIME';

-- 2. 添加索引
CREATE INDEX `idx_user_tier` ON `user` (`user_tier`);

-- 3. 创建测试VIP用户（可选）
UPDATE user SET user_tier = 'VIP_MONTHLY' WHERE userId IN (1, 2, 3);
UPDATE user SET user_tier = 'VIP_YEARLY' WHERE userId IN (4, 5);
```

### 2.2 验证数据库变更
```sql
-- 检查字段是否添加成功
DESCRIBE user;

-- 查看用户等级分布
SELECT user_tier, COUNT(*) as count FROM user GROUP BY user_tier;
```

## 3. API接口测试

### 3.1 获取数据恢复权限信息
```
GET /api/v2/datarecovery/getRecoveryPermission
```

**响应示例（免费用户）：**
```json
{
  "success": true,
  "data": {
    "hasPermission": true,
    "userTier": "FREE",
    "userTierDisplay": "免费用户",
    "recoveryPeriodDays": 7,
    "earliestRecoveryDate": "2024-07-15T00:00:00",
    "upgradeMessage": "升级VIP可恢复6个月内的数据",
    "upgradeRequired": false
  }
}
```

**响应示例（VIP用户）：**
```json
{
  "success": true,
  "data": {
    "hasPermission": true,
    "userTier": "VIP_MONTHLY",
    "userTierDisplay": "VIP月度",
    "recoveryPeriodDays": 180,
    "earliestRecoveryDate": "2024-01-22T00:00:00",
    "upgradeMessage": null,
    "upgradeRequired": false
  }
}
```

### 3.2 查询已删除数据
```
GET /api/v2/datarecovery/getDeletedData?dataType=template&pageNumber=1&pageSize=10
```

**响应变化：**
- 现在包含权限信息
- 只返回在用户权限范围内的数据
- 免费用户只能看到7天内删除的数据
- VIP用户可以看到6个月内删除的数据

### 3.3 恢复数据
```
POST /api/v2/datarecovery/recoverSingleData
{
  "dataType": "template",
  "dataId": "123"
}
```

**权限验证：**
- 如果数据超出用户的恢复期限，返回错误：
  ```json
  {
    "success": false,
    "msg": "数据超出可恢复时间范围，请升级VIP获得更长恢复期限"
  }
  ```

## 4. 测试用例

### 4.1 免费用户测试
1. **准备测试数据**
   - 创建一些模板并删除（分别在1天前、5天前、10天前、30天前）
   
2. **测试查询权限**
   - 免费用户应该只能看到7天内删除的数据
   - 10天前和30天前删除的数据不应该出现在列表中

3. **测试恢复权限**
   - 尝试恢复7天内的数据：应该成功
   - 尝试恢复10天前的数据：应该失败并提示升级VIP

### 4.2 VIP用户测试
1. **准备测试数据**
   - 创建一些模板并删除（分别在1天前、30天前、90天前、200天前）
   
2. **测试查询权限**
   - VIP用户应该能看到180天内删除的数据
   - 200天前删除的数据不应该出现在列表中

3. **测试恢复权限**
   - 尝试恢复180天内的数据：应该成功
   - 尝试恢复200天前的数据：应该失败

### 4.3 权限升级测试
1. **用户等级变更**
   ```sql
   -- 将免费用户升级为VIP
   UPDATE user SET user_tier = 'VIP_MONTHLY' WHERE userId = 1;
   ```

2. **验证权限变化**
   - 升级后用户应该能看到更多历史删除数据
   - 之前无法恢复的数据现在应该可以恢复

## 5. 测试脚本

### 5.1 创建测试数据脚本
```sql
-- 创建不同时间的删除数据用于测试
-- 注意：需要根据实际的表结构调整

-- 1天前删除的模板
INSERT INTO templet (userId, name, deleteTime, createTime) 
VALUES (1, '测试模板1天前', DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 10 DAY));

-- 10天前删除的模板
INSERT INTO templet (userId, name, deleteTime, createTime) 
VALUES (1, '测试模板10天前', DATE_SUB(NOW(), INTERVAL 10 DAY), DATE_SUB(NOW(), INTERVAL 20 DAY));

-- 30天前删除的模板
INSERT INTO templet (userId, name, deleteTime, createTime) 
VALUES (1, '测试模板30天前', DATE_SUB(NOW(), INTERVAL 30 DAY), DATE_SUB(NOW(), INTERVAL 40 DAY));

-- 200天前删除的模板
INSERT INTO templet (userId, name, deleteTime, createTime) 
VALUES (1, '测试模板200天前', DATE_SUB(NOW(), INTERVAL 200 DAY), DATE_SUB(NOW(), INTERVAL 210 DAY));
```

### 5.2 验证查询结果
```sql
-- 验证免费用户只能看到7天内的数据
SELECT name, deleteTime, DATEDIFF(NOW(), deleteTime) as days_ago
FROM templet 
WHERE userId = 1 AND deleteTime IS NOT NULL 
AND deleteTime >= DATE_SUB(NOW(), INTERVAL 7 DAY)
ORDER BY deleteTime DESC;

-- 验证VIP用户可以看到180天内的数据
SELECT name, deleteTime, DATEDIFF(NOW(), deleteTime) as days_ago
FROM templet 
WHERE userId = 1 AND deleteTime IS NOT NULL 
AND deleteTime >= DATE_SUB(NOW(), INTERVAL 180 DAY)
ORDER BY deleteTime DESC;
```

## 6. 注意事项

### 6.1 兼容性
- 现有API接口保持兼容
- 只是在响应中增加了权限信息
- 现有的前端代码无需修改即可工作

### 6.2 性能考虑
- 权限检查增加了少量数据库查询
- 建议在高并发环境下考虑缓存用户权限信息

### 6.3 错误处理
- 所有权限检查都有完善的错误处理
- 默认采用安全策略（权限不足时拒绝访问）

## 7. 后续扩展

### 7.1 完整VIP体系
- 当前实现是最小化版本，专注于数据恢复功能
- 后续可以扩展为完整的VIP订阅、支付系统
- 可以添加更多VIP功能（OCR、团队协作等）

### 7.2 权限缓存
- 可以考虑使用Redis缓存用户权限信息
- 提高高并发场景下的性能

### 7.3 监控和统计
- 添加VIP功能使用统计
- 监控免费用户的升级转化率
