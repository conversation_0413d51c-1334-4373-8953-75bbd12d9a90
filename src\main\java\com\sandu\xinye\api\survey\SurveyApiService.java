package com.sandu.xinye.api.survey;

import com.jfinal.kit.Ret;
import com.mysql.cj.jdbc.exceptions.SQLError;
import com.sandu.xinye.admin.operate.OperationLogService;
import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.Survey;
import com.sandu.xinye.common.model.SurveyAnswer;
import com.sandu.xinye.common.model.SurveyQuestion;
import com.sandu.xinye.common.model.SysUser;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

public class SurveyApiService {
    private static final org.apache.log4j.Logger logger = org.apache.log4j.Logger.getLogger(SurveyApiService.class);

    public static final SurveyApiService me = new SurveyApiService();

    public RetKit isUserFinished(Integer userId) {
        SurveyAnswer model = SurveyAnswer.dao.findFirst("select * from survey_answer where userId=?", userId);
        return RetKit.ok("data", model != null);
    }

    public RetKit getNewestSurvey() {
        Survey survey = Survey.dao.findFirst("select * from survey order by id desc");
        return RetKit.ok("data", survey);
    }

    public RetKit getNewestQuestions() {
        List<SurveyQuestion> list = SurveyQuestion.dao.find("select * from survey_question q where q.surveyId = (select id from survey order by id desc limit 1 )");
        return RetKit.ok("list", list);
    }

    public RetKit saveAnswer(Integer surveyId, Integer userId, String answer, String ip) {
        SurveyAnswer model = new SurveyAnswer();
        boolean succ = false;
        try{
            succ = model.setSurveyId(surveyId).setAnswer(answer).setUserId(userId).setCreateTime(new Date()).save();
        }catch (Exception error){
            logger.error(String.format("用户【%s】提交问卷调查【%s】保存失败", userId, surveyId),error);
            return RetKit.fail();
        }

        if (succ) {
            String content = userId + "添加了问卷调查ID为" + surveyId + "的答案";
            OperationLogService.me.saveOperationLog(userId, ip, content);
        }
        return succ ? RetKit.ok() : RetKit.fail();
    }

}
