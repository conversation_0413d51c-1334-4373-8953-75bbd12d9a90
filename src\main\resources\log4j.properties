log4j.rootLogger=INFO, stdout, file
# \u8BBE\u7F6E com.vida.xinye \u5305\u4E0B\u7684\u65E5\u5FD7\u7EA7\u522B\u4E3A DEBUG
log4j.logger.com.sandu.xinye=INFO
###
log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.Target=System.out
log4j.appender.stdout.Encoding=UTF-8
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=%n[%-d{yyyy-MM-dd HH:mm:ss}]-[%p]-[%C.%M():%L]-[%m]%n
# Output to the File
log4j.appender.file=org.apache.log4j.RollingFileAppender
log4j.appender.file.Threshold=INFO
log4j.appender.file.File=./logs/xinye.log
log4j.appender.file.Encoding=UTF-8
log4j.appender.file.Append=true 
# \u6587\u4EF6\u8D85\u51FA10MB\u5C31\u521B\u5EFA\u65B0\u7684\u65E5\u5FD7\u6587\u4EF6
log4j.appender.file.MaxFileSize=10240KB 
log4j.appender.file.MaxBackupIndex=60
log4j.appender.file.layout=org.apache.log4j.PatternLayout
log4j.appender.file.layout.ConversionPattern=%n[%-d{yyyy-MM-dd HH:mm:ss}]-[%p]-[%C.%M():%L]-[%m]%n
#\u529F\u80FD\u8017\u65F6\u3001\u5F02\u5E38\u6355\u83B7\u65E5\u5FD7
#log4j.logger.timeAround=info,timeAround
#log4j.appender.timeAround=org.apache.log4j.RollingFileAppender
#log4j.appender.timeAround.File=./logs/timeAround.log
#log4j.appender.timeAround.MaxFileSize=200MB
#log4j.appender.timeAround.MaxBackupIndex=10
#log4j.appender.timeAround.layout=org.apache.log4j.PatternLayout
#log4j.appender.timeAround.layout.ConversionPattern=%n[%-d{yyyy-MM-dd HH:mm:ss}]-[%p]-[%C.%M():%L]-[%m]%n
#log4j.additivity.timeAround=false
