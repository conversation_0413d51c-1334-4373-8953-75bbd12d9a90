package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseEverydayData<M extends BaseEverydayData<M>> extends Model<M> implements IBean {

	public M setId(java.lang.Integer id) {
		set("id", id);
		return (M)this;
	}
	
	public java.lang.Integer getId() {
		return getInt("id");
	}

	public M setNewUserNumber(java.lang.Integer newUserNumber) {
		set("newUserNumber", newUserNumber);
		return (M)this;
	}
	
	public java.lang.Integer getNewUserNumber() {
		return getInt("newUserNumber");
	}

	public M setLoginUserNumber(java.lang.Integer loginUserNumber) {
		set("loginUserNumber", loginUserNumber);
		return (M)this;
	}
	
	public java.lang.Integer getLoginUserNumber() {
		return getInt("loginUserNumber");
	}

	public M setCreateTime(java.util.Date createTime) {
		set("createTime", createTime);
		return (M)this;
	}
	
	public java.util.Date getCreateTime() {
		return get("createTime");
	}

}
