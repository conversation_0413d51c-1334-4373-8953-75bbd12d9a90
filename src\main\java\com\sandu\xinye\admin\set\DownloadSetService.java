package com.sandu.xinye.admin.set;

import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.DownloadSet;

import java.util.Date;

public class DownloadSetService {

    public static final DownloadSetService me = new DownloadSetService();


    public RetKit getSet() {
        DownloadSet model = DownloadSet.dao.findFirst("select id,url from download_set");
        if (model == null) {
            model = new DownloadSet();
            model.setUrl("").setCreateTime(new Date()).setUpdateTime(new Date());
            model.save();
        }
        return RetKit.ok("model", model);
    }

    public RetKit update(String id, String url) {
        if (url == null || url.trim().equals("")) {
            return RetKit.fail("请输入URL地址");
        }
        DownloadSet downloadSet = DownloadSet.dao.findById(id);
        downloadSet.setUrl(url);
        downloadSet.setUpdateTime(new Date());
        boolean success = downloadSet.update();
        return success ? RetKit.ok() : RetKit.fail();

    }
}
