package com.sandu.xinye.api.v2.user.validator;

import com.jfinal.core.Controller;
import com.sandu.xinye.common.validator.BaseValidator;

public class UpdatePwdValidator extends BaseValidator {

	@Override
	protected void validate(Controller c) {
		validateEqual("old_password", "new_password","msg", "两次密码输入不一致");
		validateParaNotNull("new_password", "新密码");
		validateParaNotNull("old_password", "原始密码");
	}

	@Override
	protected void handleError(Controller c) {
		renderErroJson(c);
	}

}
