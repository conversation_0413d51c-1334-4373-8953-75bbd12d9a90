package com.sandu.xinye.api.templet.validator;

import com.jfinal.core.Controller;
import com.sandu.xinye.common.validator.BaseValidator;

public class TempletGroupValidator extends BaseValidator {

	@Override
	protected void validate(Controller c) {
		String methodName = getActionMethodName();

		if (methodName.equals("addGroup")) {
			validateParaNotNull("name", "分组名称");
			validateLength("name", 32, "msg", "分组名称长度不超过32个字符");
		} else if (methodName.equals("moveTempletToGroup")) {
			validateParaNotNull("templetId", "模板id");
			validateParaNotNull("groupId", "分组id");
		} else if (methodName.equals("updateTempletGroupName")) {
			validateParaNotNull("id", "分组id");
			validateParaNotNull("name", "分组名称");
			validateLength("name", 32, "msg", "分组名称长度不超过32个字符");
		} else if (methodName.equals("deleteTempletGroup")) {
			validateParaNotNull("groupId", "分组id");
		}
	}

	@Override
	protected void handleError(Controller c) {
		renderErroJson(c);
	}

}
