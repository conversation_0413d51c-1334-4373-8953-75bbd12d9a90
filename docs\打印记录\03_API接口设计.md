# 打印记录API接口设计

## 1. 接口概览

基于RESTful设计原则，设计打印记录相关的API接口。

### 1.1 接口前缀
```
/api/v2/printRecord
```

### 1.2 接口列表
| 接口 | 方法 | 描述 |
|------|------|------|
| `/list` | GET | 获取打印记录列表 |
| `/search` | GET | 搜索打印记录 |
| `/delete` | POST | 删除打印记录 |
| `/detail/{id}` | GET | 获取打印记录详情 |

## 2. 接口详细设计

### 2.1 获取打印记录列表

**接口地址：** `GET /api/v2/printRecord/list`

**请求参数：**
```json
{
  "printType": 1,        // 必填，打印类型：1-模板打印，2-文档打印，3-图片打印
  "pageNumber": 1,       // 可选，页码，默认1
  "pageSize": 100        // 可选，页大小，模板打印默认100，文档/图片默认20
}
```

**响应格式：**
```json
{
  "state": "ok",
  "data": {
    "list": [
      {
        "id": 1,
        "sourceId": 123,
        "sourceName": "商品标签模板",
        "sourceCover": "http://example.com/cover.jpg",
        "printWidth": 50,
        "printHeight": 30,
        "printCopies": 5,
        "printPlatform": 1,           // 1-iOS, 2-Android, 3-Windows, 4-Mac
        "printPlatformName": "手机iOS",
        "printTime": "2024-01-15 14:30:00",
        "fileType": null,
        "fileSize": null,
        "printStatus": 1,
        "printStatusName": "成功"
      }
    ],
    "totalRow": 150,
    "pageNumber": 1,
    "pageSize": 100,
    "totalPage": 2
  }
}
```

### 2.2 搜索打印记录

**接口地址：** `GET /api/v2/printRecord/search`

**请求参数：**
```json
{
  "printType": 1,        // 必填，打印类型
  "keyword": "商品",      // 可选，搜索关键词（按名称搜索）
  "width": 50,           // 可选，打印宽度（仅模板打印支持）
  "height": 30,          // 可选，打印高度（仅模板打印支持）
  "pageNumber": 1,       // 可选，页码，默认1
  "pageSize": 20         // 可选，页大小，默认20
}
```

**响应格式：**
```json
{
  "state": "ok",
  "data": {
    "list": [
      // 同列表接口格式
    ],
    "totalRow": 10,
    "pageNumber": 1,
    "pageSize": 20,
    "totalPage": 1
  }
}
```

### 2.3 删除打印记录

**接口地址：** `POST /api/v2/printRecord/delete`

**请求参数：**
```json
{
  "id": 123              // 必填，打印记录ID
}
```

**响应格式：**
```json
{
  "state": "ok",
  "msg": "删除成功"
}
```

### 2.4 获取打印记录详情

**接口地址：** `GET /api/v2/printRecord/detail/{id}`

**路径参数：**
- `id`: 打印记录ID

**响应格式：**
```json
{
  "state": "ok",
  "data": {
    "id": 1,
    "sourceId": 123,
    "sourceName": "商品标签模板",
    "sourceCover": "http://example.com/cover.jpg",
    "printWidth": 50,
    "printHeight": 30,
    "printCopies": 5,
    "printPlatform": 1,
    "printPlatformName": "手机iOS",
    "printTime": "2024-01-15 14:30:00",
    "sourceData": "{...}",        // 源数据JSON
    "fileType": null,
    "fileSize": null,
    "printStatus": 1,
    "printStatusName": "成功",
    "createTime": "2024-01-15 14:30:00",
    "updateTime": "2024-01-15 14:30:00"
  }
}
```

## 3. 错误码定义

| 错误码 | 描述 |
|--------|------|
| 40001 | 参数错误 |
| 40002 | 打印记录不存在 |
| 40003 | 无权限访问该记录 |
| 40004 | 打印类型不支持 |
| 50001 | 服务器内部错误 |

## 4. 数据字典

### 4.1 打印类型 (printType)
- 1: 模板打印
- 2: 文档打印
- 3: 图片打印

### 4.2 打印端类型 (printPlatform)
- 1: 手机iOS
- 2: 手机安卓
- 3: 电脑Windows
- 4: 电脑Mac

### 4.3 打印状态 (printStatus)
- 1: 成功
- 2: 失败

## 5. 接口调用示例

### 5.1 获取模板打印记录
```bash
curl -X GET "http://api.example.com/api/v2/printRecord/list?printType=1&pageNumber=1&pageSize=100" \
  -H "Authorization: Bearer {token}"
```

### 5.2 搜索模板打印记录
```bash
curl -X GET "http://api.example.com/api/v2/printRecord/search?printType=1&keyword=商品&width=50&height=30" \
  -H "Authorization: Bearer {token}"
```

### 5.3 删除打印记录
```bash
curl -X POST "http://api.example.com/api/v2/printRecord/delete" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{"id": 123}'
```

## 6. 注意事项

1. 所有接口都需要用户认证
2. 用户只能访问自己的打印记录
3. 删除操作为软删除，不物理删除数据
4. 分页查询需要考虑性能优化
5. 搜索功能支持模糊匹配
6. 尺寸搜索仅模板打印支持
