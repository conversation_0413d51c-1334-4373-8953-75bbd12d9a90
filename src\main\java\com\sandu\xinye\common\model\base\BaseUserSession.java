package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JF<PERSON>, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseUserSession<M extends BaseUserSession<M>> extends Model<M> implements IBean {

	public M setSessionId(java.lang.String sessionId) {
		set("sessionId", sessionId);
		return (M)this;
	}
	
	public java.lang.String getSessionId() {
		return getStr("sessionId");
	}

	public M setUserId(java.lang.Integer userId) {
		set("userId", userId);
		return (M)this;
	}
	
	public java.lang.Integer getUserId() {
		return getInt("userId");
	}

	public M setTimeStamp(java.lang.Long timeStamp) {
		set("timeStamp", timeStamp);
		return (M)this;
	}
	
	public java.lang.Long getTimeStamp() {
		return getLong("timeStamp");
	}

	public M setCreateTime(java.util.Date createTime) {
		set("createTime", createTime);
		return (M)this;
	}
	
	public java.util.Date getCreateTime() {
		return get("createTime");
	}

}
