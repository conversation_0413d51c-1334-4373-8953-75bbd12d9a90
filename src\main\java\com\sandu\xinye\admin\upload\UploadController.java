package com.sandu.xinye.admin.upload;

import com.jfinal.upload.UploadFile;
import com.sandu.xinye.common.controller.AdminController;
import com.sandu.xinye.common.kit.RetKit;

import java.util.List;

public class UploadController extends AdminController {
	
	public void uploadImg(){
		UploadFile uf = getFile("file");
		RetKit ret = UploadService.me.uploadImg(uf);
		renderJson(ret);
	}

	public void uploadLogo(){
		UploadFile uf = getFile("file");
		RetKit ret = UploadService.me.uploadLogo(uf);
		renderJson(ret);
	}
	
	public void uploadFont(){
		UploadFile uf = getFile("file");
		String prefix = getPara("prefix");
		RetKit ret = UploadService.me.uploadFont(uf, prefix);
		renderJson(ret);
	}

	public void uploadFontCover(){
		UploadFile uf = getFile("file");
		String prefix = getPara("prefix");
		RetKit ret = UploadService.me.uploadFontCover(uf, prefix);
		renderJson(ret);
	}
	
	public void uploadVideo(){
		UploadFile uf = getFile("file");
		RetKit ret = UploadService.me.uploadVideo(uf);
		renderJson(ret);
	}
	
	public void uploadApk(){
		UploadFile uf = getFile("file");
		RetKit ret = UploadService.me.uploadApk(uf);
		renderJson(ret);
	}
}
