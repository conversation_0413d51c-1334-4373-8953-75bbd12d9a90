package com.sandu.xinye.api.survey;

import com.jfinal.aop.Before;
import com.jfinal.plugin.activerecord.Record;
import com.sandu.xinye.api.survey.validator.SurveyValidator;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.kit.RetKit;

public class SurveyApiController extends AppController {

	/**
	 * @Title: getNewestQuestions
	 * @Description: 获取最新的问卷调查（问题）
	 * @date 2022年02月28日
	 * <AUTHOR>
	 */
	public void getNewest() {
		RetKit ret = SurveyApiService.me.getNewestSurvey();
		renderJson(ret);
	}

	/**
	 * @Title: getNewestQuestions
	 * @Description: 获取最新的问卷调查（问题）
	 * @date 2022年02月28日
	 * <AUTHOR>
	 */
	public void getNewestQuestions() {
		RetKit ret = SurveyApiService.me.getNewestQuestions();
		renderJson(ret);
	}

	/**
	 * @Title: addSurveyAnswer
	 * @Description: 保存问卷调查（答案
	 * @date 2022年02月28日
	 * <AUTHOR>
	 */
	@Before({SurveyValidator.class})
	public void save(){
		Record bodyPara = getArgsRecord();
		Integer surveyId = bodyPara.getInt("surveyId");
		Integer userId = getUser().getUserId();
		String answer = bodyPara.getStr("answer");

		RetKit ret = SurveyApiService.me.saveAnswer(surveyId, userId, answer, getIpAddress());
		renderJson(ret);
	}

	public void isFinished(){
		RetKit ret = SurveyApiService.me.isUserFinished(getUser().getUserId());
		renderJson(ret);
	}
}
