CREATE TABLE IF NOT EXISTS product_info (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(50) NOT NULL COMMENT '条形码',
    goods_name VARCHAR(200) COMMENT '商品名称',
    manu_name VARCHAR(200) COMMENT '厂商',
    spec VARCHAR(100) COMMENT '规格',
    price VARCHAR(20) COMMENT '参考价格',
    trademark VARCHAR(100) COMMENT '商标/品牌名称',
    img VARCHAR(500) COMMENT '图片地址',
    goods_type VARCHAR(200) COMMENT '商品分类',
    sptm_img VARCHAR(500) COMMENT '条码图片',
    ycg VARCHAR(100) COMMENT '原产地',
    eng_name VARCHAR(200) COMMENT '英文名称',
    note TEXT COMMENT '备注信息',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    update_time DATETIME NOT NULL COMMENT '更新时间',
    UNIQUE KEY uk_code (code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品信息表'; 