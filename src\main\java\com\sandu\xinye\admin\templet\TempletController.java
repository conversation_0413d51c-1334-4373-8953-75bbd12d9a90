package com.sandu.xinye.admin.templet;

import com.jfinal.aop.Clear;
import com.jfinal.kit.LogKit;
import com.sandu.xinye.common.controller.AdminController;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.Templet;
import com.xiaoleilu.hutool.util.StrUtil;

public class TempletController extends AdminController {

	@Clear
	public void index(){
		render("/admin_index.html");
	}

	public void list(){
		int pageNumber = getParaToInt("pageNumber",1);
		int pageSize = getParaToInt("pageSize",10);
		RetKit ret = TempletService.me.list(pageSize, pageNumber,getParaToMap());
		renderJson(ret);
	}

	public void add(){
		Templet templet = getBean(Templet.class,"");
		RetKit ret = TempletService.me.add(templet, getAccount(),getIpAddress());
		renderJson(ret);
	}

	public void update(){
		Templet templet = getBean(Templet.class,"");
		RetKit ret = TempletService.me.update(templet, getAccount(),getIpAddress());
		renderJson(ret);
	}

	public void del(){
		String groupId = getPara(0);
		RetKit ret = TempletService.me.del(groupId, getAccount(),getIpAddress());
		renderJson(ret);
	}

	public void getTempletList(){
		String userId = getPara("userId");
		String groupId = getPara("groupId");
		String type = getPara("type");
		RetKit ret = TempletService.me.getTempletList(userId, groupId, type);
		renderJson(ret);
	}
  /**
   *判断69码是否已经存在
   */
  public void isExistEancode() {
    String eancode = getPara(0);
    RetKit ret = TempletService.me.isExistEancode(eancode);
    renderJson(ret);
  }


	/**
	 * 用户模板上传为行业模板
	 *
	 */
	public void uploadToCloud(){
		int cloudGroupId = getParaToInt("groupId", -1);
		int templetId = getParaToInt("templetId", -1);
		String serverPath = getServerUrl();
		LogKit.info("服务器地址：" + serverPath);
		RetKit ret = TempletService.me.uploadToCloud(serverPath, templetId, cloudGroupId, getAccount() ,getIpAddress());
		renderJson(ret);
	}

	/**
	 * 用户模板批量上传为行业模板
	 */
	public void batchUploadToCloud() {
		String ids = getPara("ids");
		LogKit.error("batchUploadTocloud ids: "+ ids);
		if (StrUtil.isEmpty(ids)) {
			renderError(400, "上传数据不存在！");
		}

		String[] templetIdArray = ids.split(",");
		int cloudGroupId = getParaToInt("groupId", -1);

		String serverPath = getServerUrl();
		LogKit.info("batchUploadToCloud->> 服务器地址：" + serverPath);
		RetKit ret = TempletService.me.batchUploadToCloud(serverPath, templetIdArray, cloudGroupId, getAccount(), getIpAddress());
		renderJson(ret);
	}

}
