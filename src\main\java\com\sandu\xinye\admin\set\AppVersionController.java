package com.sandu.xinye.admin.set;

import com.jfinal.aop.Clear;
import com.sandu.xinye.common.controller.AdminController;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.AppVersion;

public class AppVersionController extends AdminController {
	
	@Clear
	public void index(){
		render("/admin_index.html");
	}

	public void list(){
		int pageNumber = getParaToInt("pageNumber",1);
		int pageSize = getParaToInt("pageSize",10);
		RetKit ret = AppVersionService.me.list(pageSize, pageNumber);
		renderJson(ret);
	}
	
	public void add(){
		AppVersion version = getBean(AppVersion.class,"");
		RetKit ret = AppVersionService.me.add(version, getAccount(),getIpAddress());
		renderJson(ret);
	}
	
	public void update(){
		AppVersion version = getBean(AppVersion.class,"");
		RetKit ret = AppVersionService.me.update(version, getAccount(),getIpAddress());
		renderJson(ret);
	}
	
	public void del(){
		String id = getPara(0);
		RetKit ret = AppVersionService.me.del(id, getAccount(),getIpAddress());
		renderJson(ret);
	}
}
