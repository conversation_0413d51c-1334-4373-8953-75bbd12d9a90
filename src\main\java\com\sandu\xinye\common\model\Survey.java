package com.sandu.xinye.common.model;

import com.sandu.xinye.common.model.base.BaseSurvey;

import java.util.List;

/**
 * Generated by JFinal.
 */
@SuppressWarnings("serial")
public class Survey extends BaseSurvey<Survey> {
	public static final Survey dao = new Survey().dao();

	public List<SurveyQuestion> getSurveyQuestions() {
		return SurveyQuestion.dao.find("select * from blog where surveyId=?", get("surveyId"));
	}

}
