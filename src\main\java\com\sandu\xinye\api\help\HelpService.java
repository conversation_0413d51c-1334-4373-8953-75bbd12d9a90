package com.sandu.xinye.api.help;

import java.util.List;

import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.Help;

public class HelpService {

	public static final HelpService me = new HelpService();


	/**
	 * @Title: getHelp
	 * @Description:  
	 * @param helpKind
	 * @return
	 * @date 2019年3月13日  上午10:53:42
	 * <AUTHOR>
	 */
	public RetKit getHelp(int helpKind) {
		List<Help> list = Help.dao.find("select * from help where helpKind = ?  order by createTime desc", helpKind);
		return RetKit.ok("list", list);
	}

}
