package com.sandu.xinye.common.interceptor;

import com.jfinal.aop.Interceptor;
import com.jfinal.aop.Invocation;
import com.jfinal.core.Controller;
import com.jfinal.i18n.I18n;
import com.jfinal.i18n.Res;
import com.jfinal.render.JsonRender;
import com.jfinal.render.Render;
import com.xiaoleilu.hutool.json.JSONObject;
import com.xiaoleilu.hutool.json.JSONUtil;
import org.apache.log4j.Logger;

import java.util.HashMap;
import java.util.Map;
import com.jfinal.kit.PropKit;
import java.util.Locale;
import com.jfinal.kit.StrKit;


public class I18nInterceptor implements Interceptor {
    private static final Logger logger = Logger.getLogger(I18nInterceptor.class);
    private static final Map LOCALE_MAP = new HashMap();

    static {
        LOCALE_MAP.put("en-US", String.valueOf(Locale.US));
        LOCALE_MAP.put("zh-CN", String.valueOf(Locale.SIMPLIFIED_CHINESE));
        LOCALE_MAP.put("zh-HK", "zh_HK");
        LOCALE_MAP.put("ko-KR", String.valueOf(Locale.KOREA));
        LOCALE_MAP.put("ja-JP", String.valueOf(Locale.JAPAN));
    }

    @Override
    public void intercept(Invocation inv) {
        Controller con = inv.getController();
        String locale = con.getHeader("Accept-Language");
//        logger.info("-----------> Accept-Language : " + locale);
        if (locale == null) {
            inv.invoke();
            return;
        }

        locale = locale.split(",")[0];
        if (LOCALE_MAP.containsKey(locale)) {
            locale = (String) LOCALE_MAP.get(locale);
        } else {
            locale = PropKit.get("i18n.defaultLocale");
        }

        inv.invoke();

        // i18n
        Render r = inv.getController().getRender();
        if (r instanceof JsonRender) {
            String JsonText = ((JsonRender) r).getJsonText();
            // JsonText 处理
            JSONObject jsonObject = JSONUtil.parseObj(JsonText);
            try {
                if (StrKit.isBlank(locale)) {
                    locale = "zh_CN";
                }
                Res resLocale = I18n.use(locale);
//                logger.info("-----------> i18n " + locale);
                String msg = (String) jsonObject.get("msg");
//                logger.info("-----------> origin msg " + msg);
                String localeMsg = resLocale.get(msg);
                if (resLocale != null && localeMsg != null) {
//                    logger.info("-----------> locale msg " + localeMsg);
                    jsonObject.put("msg", localeMsg);
                    inv.getController().renderText(JSONUtil.toJsonStr(jsonObject));
                }
            } catch (Exception e) {
                logger.error(e.getMessage());
            }

        }

    }

}
