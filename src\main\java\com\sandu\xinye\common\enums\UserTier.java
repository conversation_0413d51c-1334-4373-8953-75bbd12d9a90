package com.sandu.xinye.common.enums;

/**
 * 用户等级枚举
 * 用于区分免费用户和VIP用户的权限等级
 */
public enum UserTier {
    /**
     * 免费用户
     */
    FREE("免费用户", 0, "basic"),
    
    /**
     * VIP月度用户
     */
    VIP_MONTHLY("VIP月度", 1, "premium"),
    
    /**
     * VIP年度用户
     */
    VIP_YEARLY("VIP年度", 2, "premium_plus"),
    
    /**
     * VIP终身用户
     */
    VIP_LIFETIME("VIP终身", 3, "ultimate");
    
    private final String displayName;
    private final int level;
    private final String planCode;
    
    UserTier(String displayName, int level, String planCode) {
        this.displayName = displayName;
        this.level = level;
        this.planCode = planCode;
    }
    
    /**
     * 获取显示名称
     */
    public String getDisplayName() {
        return displayName;
    }
    
    /**
     * 获取等级数值
     */
    public int getLevel() {
        return level;
    }
    
    /**
     * 获取套餐代码
     */
    public String getPlanCode() {
        return planCode;
    }
    
    /**
     * 判断是否为VIP用户
     */
    public boolean isVip() {
        return this.level > 0;
    }
    
    /**
     * 判断是否比另一个等级高
     */
    public boolean hasHigherLevelThan(UserTier other) {
        return this.level > other.level;
    }
    
    /**
     * 根据字符串获取用户等级
     */
    public static UserTier fromString(String tierStr) {
        if (tierStr == null || tierStr.trim().isEmpty()) {
            return FREE;
        }
        
        try {
            return UserTier.valueOf(tierStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            return FREE; // 默认返回免费用户
        }
    }
    
    /**
     * 获取数据恢复期限描述
     * 注意：实际天数会根据当前时间动态计算，此方法仅用于显示描述
     */
    public String getDataRecoveryPeriodDescription() {
        switch (this) {
            case FREE:
                return "7天"; // 免费用户7天
            case VIP_MONTHLY:
            case VIP_YEARLY:
            case VIP_LIFETIME:
                return "6个月"; // VIP用户6个月
            default:
                return "7天";
        }
    }

    /**
     * 获取数据恢复期限（天数）
     * @deprecated 使用VipPermissionService.calculateActualRecoveryPeriodDays()获取准确天数
     */
    @Deprecated
    public int getDataRecoveryPeriodDays() {
        switch (this) {
            case FREE:
                return 7; // 免费用户7天
            case VIP_MONTHLY:
            case VIP_YEARLY:
            case VIP_LIFETIME:
                return 180; // VIP用户约6个月（实际天数会动态计算）
            default:
                return 7;
        }
    }
}
