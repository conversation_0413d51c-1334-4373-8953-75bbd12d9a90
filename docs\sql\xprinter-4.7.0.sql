CREATE TABLE `user_preference` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `unit` TINYINT(2) DEFAULT NULL COMMENT '模板单位',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户通用个性化设置';

ALTER TABLE `user_preference`
ADD COLUMN `lang` VARCHAR(10) DEFAULT NULL COMMENT '语言';

 ALTER TABLE `user_preference`
ADD COLUMN `safe_mode` TINYINT(2) DEFAULT NULL COMMENT '安全模式';