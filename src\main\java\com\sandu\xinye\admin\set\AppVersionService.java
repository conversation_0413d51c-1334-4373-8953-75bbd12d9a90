package com.sandu.xinye.admin.set;

import java.io.File;
import java.sql.SQLException;
import java.util.Date;

import com.jfinal.kit.PathKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.IAtom;
import com.jfinal.plugin.activerecord.Page;
import com.sandu.xinye.admin.operate.OperationLogService;
import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.kit.UploadFileMoveKit;
import com.sandu.xinye.common.model.AppVersion;
import com.sandu.xinye.common.model.SysUser;

public class AppVersionService {

	public static final AppVersionService me = new AppVersionService();
	private static final String FILE_PATH = "apk";

	public RetKit list(int pageSize, int pageNumber) {
		Page<AppVersion> page = AppVersion.dao.paginate(pageNumber, pageSize, "select * ",
				" from app_version order by id desc");
		for (AppVersion av : page.getList()) {
			SysUser su = SysUser.dao.findById(av.getSysUserId());
			av.put("sysUserName", su.getSysUserName());
		}
		return RetKit.ok("page", page);
	}

	public RetKit add(AppVersion version, SysUser account, String ipAddress) {
		String newUrl = UploadFileMoveKit.move(version.getAndroidApk(), FILE_PATH);
		boolean succ = version.setAndroidApk(newUrl).setCreateTime(new Date()).setSysUserId(account.getSysUserId())
				.save();
		if (succ) {
			String content = account.getSysUserName() + "添加了" + version.getAndroidVersion();
			OperationLogService.me.saveOperationLog(account.getSysUserId(), ipAddress, content);
		}
		return succ ? RetKit.ok() : RetKit.fail();
	}

	public RetKit update(AppVersion version, SysUser account, String ipAddress) {
		String newUrl = UploadFileMoveKit.move(version.getAndroidApk(), FILE_PATH);
		AppVersion oldAppVersion = AppVersion.dao.findById(version.getId());

		boolean success = Db.tx(new IAtom() {

			@Override
			public boolean run() throws SQLException {
				boolean succ1 = true;
				if (!oldAppVersion.getAndroidApk().equals(version.getAndroidApk())) {
					File file = new File(Constant.REAL_UPLOAD_PATH + oldAppVersion.getAndroidApk());
					if (file.exists()) {
						succ1 = file.delete();
					}
				}
				boolean succ2 = version.setAndroidApk(newUrl).setCreateTime(new Date())
						.setSysUserId(account.getSysUserId()).update();
				return succ1 && succ2;
			}

		});

		if (success) {
			String content = account.getSysUserName() + "编辑了" + version.getAndroidVersion();
			OperationLogService.me.saveOperationLog(account.getSysUserId(), ipAddress, content);
		}
		return success ? RetKit.ok() : RetKit.fail();
	}

	public RetKit del(String id, SysUser account, String ipAddress) {
		AppVersion other = AppVersion.dao.findFirst("select * from app_version where id != ?", id);
		if (other == null) {
			return RetKit.fail("该数据是最后一条记录，无法删除！");
		}
		AppVersion version = AppVersion.dao.findById(id);
		String versionName = version.getAndroidVersion();
		String filePath = PathKit.getWebRootPath() + version.getAndroidApk();
		File file = new File(filePath);
		boolean success = Db.tx(new IAtom() {

			@Override
			public boolean run() throws SQLException {
				boolean succ1 = true;
				if(file.exists()){
					succ1 = file.delete();
				}
				boolean succ2 = version.delete();
				return succ1 && succ2;
			}

		});
		if (success) {
			String content = account.getSysUserName() + "删除了" + versionName;
			OperationLogService.me.saveOperationLog(account.getSysUserId(), ipAddress, content);
		}
		return success ? RetKit.ok() : RetKit.fail();
	}

}
