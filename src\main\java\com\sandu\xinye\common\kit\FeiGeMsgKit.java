package com.sandu.xinye.common.kit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.kit.PropKit;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.HttpClientUtils;
import org.apache.http.concurrent.FutureCallback;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.impl.nio.client.HttpAsyncClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

/**
 * 飞鸽传书短信验证码工具类
 * 
 * <AUTHOR>
 * @date 2018/8/1
 */
public class FeiGeMsgKit {

	private static final Logger logger = Logger.getLogger(FeiGeMsgKit.class);

	// 用于把线程设置为停止状态，由于Post方法里需要跳到其他线程（http请求）进行异步请求，等待请求完成之后取到回调结果，再执行后面的代码。
	private CountDownLatch latch;
	private boolean result = false;

	/**
	 * @param phone 手机号码
	 * @param code  验证码
	 * @return true 发送成功， false发送失败
	 * @方法描述: 发送验证码
	 */
	public boolean send(String phone, String code) {
		logger.info("发送验证码开始----------------" + phone + " " + code);
		result = false;
		latch = new CountDownLatch(1);
		try {
			Map<String, Object> map = new HashMap<>();
			map.put("apikey", "N336956111");
			map.put("secret", "3369587f31a11f16");
			map.put("mobile", phone);
			map.put("content", "您的注册验证码是：" + code + "，请不要把验证码泄露给其他人，如非本人请忽略。");
			map.put("sign_id", PropKit.use("official_config.txt").get("signId"));

			Post(map);
			// 等待结果
			latch.await();
		} catch (Exception e) {
			logger.error(e.getMessage());
		}
		logger.info("发送验证码结束----------------" + phone + " " + code);
		return result;
	}

	/**
	 * @param hashMap 请求参数Nap
	 * @throws Exception
	 * @方法描述:Post 提交发送普通短信请求
	 */
	private void Post(Map<String, Object> hashMap) throws Exception {

		CloseableHttpAsyncClient httpClient = HttpAsyncClients.createDefault();
		httpClient.start();

		HttpPost requestPost = new HttpPost("https://api.4321.sh/sms/send");
		requestPost.addHeader("Content-Type", "application/json");

		String json = JSON.toJSONString(hashMap);
		requestPost.setEntity(new StringEntity(json, "UTF-8"));

		httpClient.execute(requestPost, new FutureCallback<HttpResponse>() {

			// 如果执行这个方法，证明中途取消了发送
			public void cancelled() {
				result = false;
				// 取消等待，继续执行后面的程序
				latch.countDown();
			}

			public void completed(HttpResponse arg0) {
				try {
					InputStream stram = arg0.getEntity().getContent();
					BufferedReader reader = new BufferedReader(new InputStreamReader(stram));
					String jsonStr = reader.readLine();
					JSONObject json = JSONObject.parseObject(jsonStr);
					String code = json.get("code").toString();
					if (code.equals("0")) {
						logger.info("发送验证码成功！");
						result = true;
					}
				} catch (UnsupportedOperationException e) {
					logger.error(e.getMessage());
				} catch (IOException e) {
					logger.error(e.getMessage());
				} finally {
					logger.info("短信发送请求结束，关闭连接！");
					HttpClientUtils.closeQuietly(arg0);
					// 无论结果如何，最后都要取消等待，继续执行后面的程序
					latch.countDown();
				}
			}

			// 如果执行这个方法，证明发送失败
			public void failed(Exception e) {
				logger.error(e.getMessage());
				result = false;
				// 取消等待，继续执行后面的程序
				latch.countDown();
			}

		}).get();
	}

	/**
	 * @方法描述:Post 提交发送国际短信请求（香港、澳門）
	 * @param phone
	 * @param code
	 * @throws Exception
	 */
	public boolean SendInternationalSms(String phone, String code) {
		boolean succ = false;
		try {
			CloseableHttpClient client = null;
			CloseableHttpResponse response = null;
			try {
				List<BasicNameValuePair> formparams = new ArrayList<>();
				formparams.add(new BasicNameValuePair("Account", "***********"));
				formparams.add(new BasicNameValuePair("Pwd", "f109876138e7d6cd97b4dfb8b"));
				formparams.add(new BasicNameValuePair("Content", "您的驗證碼是：" + code + "，請不要把驗證碼洩露個給其他人，如非本人操作請忽略。"));
				formparams.add(new BasicNameValuePair("Mobile", phone));
				formparams.add(new BasicNameValuePair("SignId", "105174"));

				HttpPost httpPost = new HttpPost("http://api.feige.ee/SmsService/Inter");
				httpPost.setEntity(new UrlEncodedFormEntity(formparams, "UTF-8"));
				client = HttpClients.createDefault();
				response = client.execute(httpPost);
				HttpEntity entity = response.getEntity();
				String result = EntityUtils.toString(entity);
				JSONObject json = JSONObject.parseObject(result);
				String resultCode = json.get("Code").toString();
				if (resultCode.equals("0")) {
					logger.info("发送验证码成功！");
					succ = true;
				} else {
					logger.info("发送验证码失败！错误代码：" + resultCode);
				}
			} finally {
				if (response != null) {
					response.close();
				}
				if (client != null) {
					client.close();
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage());
		}
		return succ;
	}

}
