package com.sandu.xinye.api.v2.cloudfile;

import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.kit.RetKit;
import com.xiaoleilu.hutool.util.StrUtil;

import java.util.Arrays;
import java.util.List;

public class CloudfileController extends AppController {

    /***
     *
     * @Title 获取我的文件列表
     * @Description
     * @return
     *
     */
    public void list() {
        RetKit ret = CloudfileService.me.getCloudfileList(getUser().getUserId());
        renderJson(ret);
    }

    /**
     * @return
     * @Title
     * @Description 上传我的文件
     * @Param name 文件名称
     * @Param url 文件地址
     * <AUTHOR>
     * @date 2021/10/14
     */
    public void upload() {
        int type = getParaToInt("type");
        String name = getPara("name");
        String url = getPara("url");
        Boolean override = getParaToBoolean("override");
        RetKit ret = CloudfileService.me.uploadCloudfile(getUser().getUserId(), type, name, url, override);
        renderJson(ret);
    }

    /**
     * @return
     * @Title
     * @Description 删除文件
     * @Param id 文件ID
     * <AUTHOR>
     * @date 2021/10/14
     */
    public void delete() {
        String fileId = getPara("id");
        if (StrUtil.isNotEmpty(fileId)) {
            List<String> fileIds = Arrays.asList(fileId.split(","));
            if (fileIds.size() == 1) {
                RetKit ret = CloudfileService.me.deleteCloudfile(getUser().getUserId(), fileId);
                renderJson(ret);
            } else {
                RetKit ret = CloudfileService.me.deleteCloudfile(getUser().getUserId(), fileIds);
                renderJson(ret);
            }
        } else {
            renderJson(RetKit.fail("参数不正确！"));
        }
    }

}
