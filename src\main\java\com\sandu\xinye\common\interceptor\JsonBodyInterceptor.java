package com.sandu.xinye.common.interceptor;

import com.alibaba.fastjson.JSON;
import com.jfinal.aop.Interceptor;
import com.jfinal.aop.Invocation;
import com.jfinal.core.Controller;
import com.jfinal.kit.JsonKit;
import com.jfinal.kit.Ret;
import com.sandu.xinye.common.annotation.JsonBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.annotation.Annotation;
import java.lang.reflect.Method;

/**
 * 拦截json请求体，body传参
 *
 * <AUTHOR>
 * @date 2024/3/14
 */
public class JsonBodyInterceptor implements Interceptor {

    @Override
    public void intercept(Invocation inv) {
        Controller controller = inv.getController();
        Method method = inv.getMethod();
        Object[] args = inv.getArgs();
        Annotation[][] annotations = method.getParameterAnnotations();
        for (int i = 0; i < args.length; i++) {
            for (Annotation annotation : annotations[i]) {
                if (annotation.annotationType() == JsonBody.class) {
                    HttpServletRequest request = controller.getRequest();
                    String contentType = request.getContentType();
                    if (contentType == null || !contentType.contains("application/json")) {
                        renderError(controller.getResponse(), "Content-Type must be application/json");
                        return;
                    }
                    String json = controller.getRawData();
                    if (json == null) {
                        renderError(controller.getResponse(), "Request body cannot be empty");
                        return;
                    }

                    try {
                        Class<?> clazz = method.getParameterTypes()[i];
                        Object obj = JSON.parseObject(json, clazz);
                        inv.setArg(0, obj);
                    } catch (Exception e) {
                        e.printStackTrace();
                        renderError(controller.getResponse(), "Error occurred while parsing JSON");
                        return;
                    }
                }
            }
        }
        inv.invoke();
    }

    private void renderError(HttpServletResponse response, String message) {
        response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
        response.setContentType("application/json;charset=UTF-8");
        Ret ret = Ret.fail("status", HttpServletResponse.SC_BAD_REQUEST).set("message", message);
        String jsonStr = JsonKit.toJson(ret);
        try {
            response.getWriter().write(jsonStr);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
