package com.sandu.xinye.admin.templet;

import com.jfinal.aop.Clear;
import com.sandu.xinye.admin.templet.TempletGroupService;
import com.sandu.xinye.common.controller.AdminController;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.TempletGroup;

public class TempletGroupController extends AdminController {
	
	@Clear
	public void index(){
		render("/admin_index.html");
	}
	
	public void list(){
		int pageNumber = getParaToInt("pageNumber",1);
		int pageSize = getParaToInt("pageSize",10);
		RetKit ret = TempletGroupService.me.list(pageSize, pageNumber,getParaToMap());
		renderJson(ret);
	}
	
	public void add(){
		TempletGroup tgroup = getBean(TempletGroup.class,"");
		RetKit ret = TempletGroupService.me.add(tgroup, getAccount(),getIpAddress());
		renderJson(ret);
	}
	
	public void update(){
		TempletGroup tgroup = getBean(TempletGroup.class,"");
		RetKit ret = TempletGroupService.me.update(tgroup, getAccount(),getIpAddress());
		renderJson(ret);
	}
	
	public void del(){
		String groupId = getPara(0);
		RetKit ret = TempletGroupService.me.del(groupId, getAccount(),getIpAddress());
		renderJson(ret);
	}

	public void getGroupList(){
		String userId = getPara("userId");
		RetKit ret = TempletGroupService.me.getGroupList(userId);
		renderJson(ret);
	}
}
