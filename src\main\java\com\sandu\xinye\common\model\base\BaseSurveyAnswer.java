package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JF<PERSON>, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseSurveyAnswer<M extends BaseSurveyAnswer<M>> extends Model<M> implements IBean {

	public M setId(java.lang.Integer id) {
		set("id", id);
		return (M)this;
	}
	
	public java.lang.Integer getId() {
		return getInt("id");
	}

	public M setSurveyId(java.lang.Integer surveyId) {
		set("surveyId", surveyId);
		return (M)this;
	}
	
	public java.lang.Long getSurveyId() {
		return getLong("surveyId");
	}

	public M setUserId(java.lang.Integer userId) {
		set("userId", userId);
		return (M)this;
	}
	
	public java.lang.Long getUserId() {
		return getLong("userId");
	}

	public M setAnswer(java.lang.String answer) {
		set("answer", answer);
		return (M)this;
	}
	
	public java.lang.String getAnswer() {
		return getStr("answer");
	}

	public M setCreateTime(java.util.Date createTime) {
		set("createTime", createTime);
		return (M)this;
	}
	
	public java.util.Date getCreateTime() {
		return get("createTime");
	}

}
