package com.sandu.xinye.api.templet;

import com.jfinal.aop.Before;
import com.sandu.xinye.api.templet.validator.TempletGroupValidator;
import com.sandu.xinye.api.templet.validator.TempletValidator;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.User;

public class TempletBusiController extends AppController {

	/**
	 * @Title: getTempletPage
	 * @Description: 获得行业模板分页
	 * @date 2021年11月16日
	 * <AUTHOR>
	 */
	public void getTempletPage() {
		int pageNumber = getParaToInt("pageNumber", 1);
		int pageSize = getParaToInt("pageSize", 10);
		String name = getPara("name");
		String widthRange = getPara("width", "");
		// 兼容旧版本不传参数
		String groupId = getPara("groupId", "");
		System.out.println("========:" + groupId);
		RetKit ret = TempletBusiService.me.getTempletPage(pageNumber, pageSize, name, groupId, widthRange);
		renderJson(ret);
	}

	/**
	 * @Title: getGroupList
	 * @Description: 获得行业模板分组列表
	 * @date 2021年11月16日
	 * <AUTHOR>
	 */
	public void getGroupList() {
		RetKit ret = TempletBusiService.me.getGroupList();
		renderJson(ret);
	}

}
