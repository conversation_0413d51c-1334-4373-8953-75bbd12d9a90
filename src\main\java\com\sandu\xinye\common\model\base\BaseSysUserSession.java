package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseSysUserSession<M extends BaseSysUserSession<M>> extends Model<M> implements IBean {

	public M setSessionId(java.lang.String sessionId) {
		set("sessionId", sessionId);
		return (M)this;
	}
	
	public java.lang.String getSessionId() {
		return getStr("sessionId");
	}

	public M setSysUserId(java.lang.Integer sysUserId) {
		set("sysUserId", sysUserId);
		return (M)this;
	}
	
	public java.lang.Integer getSysUserId() {
		return getInt("sysUserId");
	}

	public M setTimeStamp(java.lang.Long timeStamp) {
		set("timeStamp", timeStamp);
		return (M)this;
	}
	
	public java.lang.Long getTimeStamp() {
		return getLong("timeStamp");
	}

	public M setCreateTime(java.util.Date createTime) {
		set("createTime", createTime);
		return (M)this;
	}
	
	public java.util.Date getCreateTime() {
		return get("createTime");
	}

}
